#!/usr/bin/env python3

# =============================================================================
# SECTION 1: Core Imports & Environment Setup
# =============================================================================
import asyncio
import json
import os
import sys
import argparse
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

# External Dependencies
from pydantic import BaseModel, Field  # For data validation and structuring
import litellm                         # Abstraction layer for LLM API calls

# Template catalog management
class TemplateCatalog:
    """Manages template catalogs from different levels."""
    _modules = {}
    _functions = ["load_catalog", "get_sequence", "get_all_sequences", "get_system_instruction", "regenerate_catalog", "get_template"]

    @classmethod
    def register_level(cls, level, module_path):
        """Register a template catalog module for a specific level."""
        try:
            module = __import__(module_path, fromlist=["*"])
            for func_name in cls._functions:
                if not hasattr(module, func_name):
                    print(f"[Catalog] Warning: Module {module_path} missing function {func_name}")
                    return False
            cls._modules[level] = module
            return True
        except ImportError as e:
            print(f"[Catalog] Note: Level {level} templates not available ({e})")
            return False

    @classmethod
    def _apply_to_modules(cls, method_name, *args, merge_dict=False, first_result=False, extend_list=False):
        """Apply function across modules with different result handling strategies."""
        if not cls._modules:
            return {} if merge_dict else [] if extend_list else None

        if merge_dict:
            result = {"templates": {}, "sequences": {}}
            for _, module in sorted(cls._modules.items()):
                method = getattr(module, method_name)
                module_result = method(*args)
                if module_result:
                    result["templates"].update(module_result.get("templates", {}))
                    result["sequences"].update(module_result.get("sequences", {}))
            return result

        if first_result:
            for _, module in sorted(cls._modules.items()):
                method = getattr(module, method_name)
                result = method(*args)
                if result:
                    return result
            return None

        if extend_list:
            result = []
            for _, module in sorted(cls._modules.items()):
                method = getattr(module, method_name)
                module_result = method(*args)
                if module_result:
                    result.extend(module_result)
            return result

        return None

    @classmethod
    def load_catalog(cls):
        """Load and merge catalogs from all registered levels."""
        return cls._apply_to_modules("load_catalog", merge_dict=True)

    @classmethod
    def get_sequence(cls, catalog, seq_id):
        """Get a sequence from any catalog by ID."""
        return cls._apply_to_modules("get_sequence", catalog, seq_id, first_result=True)

    @classmethod
    def get_all_sequences(cls, catalog):
        """Get all sequences from all catalogs."""
        return cls._apply_to_modules("get_all_sequences", catalog, extend_list=True)

    @classmethod
    def get_template(cls, catalog, template_id):
        """Get a template from any catalog by ID."""
        return cls._apply_to_modules("get_template", catalog, template_id, first_result=True)

    @classmethod
    def get_system_instruction(cls, template_data):
        """Extract system instruction from a template."""
        if isinstance(template_data, dict) and "level" in template_data:
            level = template_data["level"]
            if level in cls._modules:
                return cls._modules[level].get_system_instruction(template_data)
        if cls._modules:
            default_level = sorted(cls._modules.keys())[0]
            return cls._modules[default_level].get_system_instruction(template_data)
        return template_data.get("raw", "") if isinstance(template_data, dict) else str(template_data)

    @classmethod
    def regenerate_catalog(cls, force=False):
        """Regenerate and merge catalogs from all registered levels."""
        return cls._apply_to_modules("regenerate_catalog", force, merge_dict=True)

# Register available template levels
# We only need level 1 now, as all templates are in the same directory
TemplateCatalog.register_level(1, "templates.lvl1_md_to_json")
# TemplateCatalog.register_level(1, "templates.lvl1.json_from_md.py")
# TemplateCatalog.register_level(1, "lvl1.md.templates.json")

# For backward compatibility and future compatibility with the old lvl2 structure
# This will be removed in a future version
# TemplateCatalog.register_level(2, "templates.lvl1.lvl1_md_to_json")

# We'll use the TemplateCatalog and SequenceManager methods directly instead of through forwarding functions

# =============================================================================
# SECTION 2: Centralized Configuration Management
# =============================================================================
class PathUtils:
    """Utilities for file path handling and directory management."""

    @staticmethod
    def get_script_dir():
        """Get current script directory."""
        return os.path.dirname(os.path.abspath(__file__))

    @staticmethod
    def join_path(*parts):
        """Join path components."""
        return os.path.join(*parts)

    @staticmethod
    def normalize_path_for_display(path):
        """Convert path to use forward slashes for display purposes."""
        if path is None:
            return None
        return str(path).replace('\\', '/')

    @staticmethod
    def get_relative_path_for_display(path):
        """Convert absolute path to relative path for display purposes."""
        if path is None:
            return None

        # Convert to absolute path first to handle any relative components
        abs_path = os.path.abspath(path)

        # Get current working directory
        cwd = os.getcwd()

        try:
            # Get relative path from current working directory
            rel_path = os.path.relpath(abs_path, cwd)
            # Convert to forward slashes for display
            return rel_path.replace('\\', '/')
        except ValueError:
            # If relative path can't be computed (e.g., different drives on Windows),
            # fall back to normalized absolute path
            return PathUtils.normalize_path_for_display(abs_path)

    @staticmethod
    def ensure_dir_exists(directory_path):
        """Create directory if it doesn't exist."""
        if not os.path.exists(directory_path):
            try:
                os.makedirs(directory_path)
                print(f"Created directory: {PathUtils.normalize_path_for_display(directory_path)}")
                return True
            except OSError as e:
                print(f"Error creating directory '{PathUtils.normalize_path_for_display(directory_path)}': {e}", file=sys.stderr)
                raise
        return True

    @staticmethod
    def sanitize_filename(filename):
        """Replace invalid filename characters."""
        sanitized = filename
        replacements = {
            "|": "+", ":": "-", "?": "_", "*": "_",
            "<": "_", ">": "_", "\"": "_", "/": "_", "\\": "_"
        }

        for char, replacement in replacements.items():
            sanitized = sanitized.replace(char, replacement)

        return sanitized

    @staticmethod
    def generate_output_filename(sequence_id, source_type, models, timestamp=None):
        """Generate standardized output filename."""
        sanitized_sequence_id = PathUtils.sanitize_filename(sequence_id)
        display_sequence_id = f"{source_type}-{sanitized_sequence_id}"

        if timestamp is None:
            timestamp = datetime.now().strftime("%Y.%m.%d-kl.%H.%M.%S")

        model_tag = "_".join(models).replace('/', '-').replace(':','-')[:30]

        return f"history--{timestamp}--{display_sequence_id}--{model_tag}.json"


class Config:
    """Manages LLM provider/model selection and settings."""

    # Environment Setup
    @staticmethod
    def _ensure_utf8_encoding():
        """Set UTF-8 output encoding for terminals."""
        for stream in (sys.stdout, sys.stderr):
            if hasattr(stream, "reconfigure"):
                try:
                    stream.reconfigure(encoding="utf-8", errors="replace")
                except Exception:
                    pass

    # Default Output Directory (subfolder within the script's own directory).
    SCRIPT_DIR = PathUtils.get_script_dir()
    DEFAULT_OUTPUT_DIR = PathUtils.join_path(SCRIPT_DIR, "output")

    # Model Registry (maps user-friendly names to actual LiteLLM model IDs)
    MODEL_REGISTRY = {
        # OpenAI
        "gpt-3.5-turbo": "gpt-3.5-turbo",
        "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",
        "gpt-4": "gpt-4",
        "gpt-4-turbo": "gpt-4-turbo",
        "gpt-4.1": "gpt-4.1",
        "gpt-4o": "gpt-4o",
        "o3-mini": "o3-mini",
        # Anthropic
        "claude-3-opus": "anthropic/claude-3-opus-20240229",
        "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
        "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
        "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",
        "claude-opus-4-20250514": "claude-opus-4-20250514",
        "claude-sonnet-4-20250514": "claude-sonnet-4-20250514",
        # Google
        "gemini-pro": "gemini/gemini-1.5-pro",
        "gemini-flash": "gemini/gemini-1.5-flash-latest",
        "gemini-2-flash": "gemini/gemini-2.0-flash",
        "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
        # Deepseek
        "deepseek-reasoner": "deepseek/deepseek-reasoner",
        "deepseek-coder": "deepseek/deepseek-coder",
        "deepseek-chat": "deepseek/deepseek-chat",
    }

    # Provider Selection
    PROVIDERS = {
        "anthropic": "anthropic",
        "deepseek": "deepseek",
        "google": "google",
        "openai": "openai"
    }

    # Default provider
    DEFAULT_PROVIDER = "openai"

    # Model configuration - consolidated structure
    MODEL_CONFIG = {
        "anthropic": {
            "models": [
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "claude-3-haiku-20240307",
                "openrouter/anthropic/claude-3.7-sonnet:beta",
                "claude-sonnet-4-20250514",
            ],
            "default": "openrouter/anthropic/claude-3.7-sonnet:beta",
            "fallback": "anthropic/claude-3-haiku-20240307"
        },
        "deepseek": {
            "models": [
                "deepseek/deepseek-reasoner",
                "deepseek/deepseek-coder",
                "deepseek/deepseek-chat"
            ],
            "default": "deepseek/deepseek-chat",
            "fallback": "deepseek/deepseek-chat"
        },
        "google": {
            "models": [
                "gemini/gemini-1.5-flash-latest",
                "gemini/gemini-2.0-flash",
                "gemini/gemini-2.5-pro-preview-03-25"
            ],
            "default": "gemini/gemini-2.5-pro-preview-03-25",
            "fallback": "gemini/gemini-1.5-flash-latest"
        },
        "openai": {
            "models": [
                "gpt-4o",
                "gpt-4o-mini",
                "gpt-3.5-turbo-instruct",
                "gpt-3.5-turbo-1106",
                "o3-mini",
                "gpt-3.5-turbo",
                "gpt-4.1"
            ],
            "default": "gpt-4.1",
            "fallback": "gpt-3.5-turbo"
        }
    }

    # For backward compatibility
    PROVIDER_ANTHROPIC = "anthropic"
    PROVIDER_DEEPSEEK = "deepseek"
    PROVIDER_GOOGLE = "google"
    PROVIDER_OPENAI = "openai"

    # For backward compatibility
    AVAILABLE_MODELS = {provider: config["models"] for provider, config in MODEL_CONFIG.items()}

    # For backward compatibility
    DEFAULT_PROVIDER_MODELS = {
        provider: {"model_name": config["default"]} for provider, config in MODEL_CONFIG.items()
    }

    # Output Directory
    @classmethod
    def set_default_output_dir(cls, directory: str):
        """Set default output directory."""
        cls.DEFAULT_OUTPUT_DIR = directory

    # Model Selection
    @classmethod
    def get_default_model(cls, provider=None):
        """Get default model for a provider."""
        provider = provider or cls.DEFAULT_PROVIDER

        if provider in cls.MODEL_CONFIG:
            return cls.MODEL_CONFIG[provider]["default"]

        # Fallback to OpenAI's default if provider not found
        return cls.MODEL_CONFIG["openai"]["fallback"]

    # Model Parameter Resolution
    @classmethod
    def get_model_params(cls, model_name=None, provider=None):
        """Resolve model name to LiteLLM ID with parameters."""
        provider = provider or cls.DEFAULT_PROVIDER
        model_name = model_name or cls.get_default_model(provider)

        # Get model ID from registry or use as-is
        actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)

        # Return model parameters
        return {"model": actual_model_id}

    # Available Models Listing
    @classmethod
    def get_available_models(cls):
        """Get models grouped by provider."""
        result = {}

        for provider, config in cls.MODEL_CONFIG.items():
            default_model = config["default"]
            provider_models = []

            for model_name in config["models"]:
                model_id = cls.MODEL_REGISTRY.get(model_name, model_name)
                provider_models.append({
                    "name": model_name,
                    "model_id": model_id,
                    "is_default": (model_name == default_model or model_id == default_model)
                })

            result[provider] = provider_models

        return result

    # LiteLLM Initialization
    @classmethod
    def configure_litellm(cls):
        """Configure LiteLLM settings."""
        litellm.drop_params = True     # Prevent errors from unsupported parameters
        litellm.num_retries = 3        # Retry failed API calls
        litellm.request_timeout = 120  # Set API request timeout
        litellm.set_verbose = False    # Reduce LiteLLM's own console output
        litellm.callbacks = []         # Disable default callbacks

        cls._ensure_utf8_encoding()



# =============================================================================
# SECTION 3: Data Structures for Configuration and Results
# =============================================================================
class ExecutorConfig(BaseModel):
    """Configuration for sequence execution."""
    # Sequence and input parameters
    sequence_steps: List[tuple] = Field(description="Step ID and template data tuples")
    user_prompt: str = Field(description="User input prompt")
    sequence_id: str = Field(description="Sequence identifier")
    models: List[str] = Field(description="Models to use")

    # Output parameters
    output_file: Optional[str] = Field(default=None, description="Output JSON file path")
    minified_output: bool = Field(default=False, description="Minify output")

    # Display options
    show_inputs: bool = Field(default=True, description="Show input prompts")
    show_system_instructions: bool = Field(default=True, description="Show system instructions")
    show_responses: bool = Field(default=True, description="Show responses")

    # Execution options
    chain_mode: bool = Field(default=True, description="Use chain mode")
    aggregator: Optional[str] = Field(default=None, description="Aggregator template ID")
    aggregator_inputs: Optional[List[str]] = Field(default=None, description="Step IDs for aggregation")

    # Aggregator options
    step_offset: int = Field(default=0, description="Step numbering offset")
    is_aggregator: bool = Field(default=False, description="Is aggregator sequence")

    # System components
    system_instruction_extractor: Callable[[Any], str] = Field(description="Instruction extractor")

    # Model parameters
    temperature: Optional[float] = Field(default=None, description="Temperature")
    max_tokens: Optional[int] = Field(default=None, description="Max tokens")

    class Config:
        arbitrary_types_allowed = True


class ModelResponse(BaseModel):
    """Model response data."""
    model: str = Field(description="Model used")
    content: str = Field(description="Response content")


class InstructionResult(BaseModel):
    """Results for one instruction step."""
    instruction: str = Field(description="Instruction used")
    step: str = Field(description="Step identifier")
    title: str = Field(description="Instruction title")
    responses: Dict[str, ModelResponse] = Field(description="Model responses")


class ExecutionResults(BaseModel):
    """Complete sequence execution results."""
    user_prompt: str = Field(description="Initial prompt")
    sequence_id: str = Field(description="Sequence ID")
    results: List[InstructionResult] = Field(description="Step results")

# =============================================================================
# SECTION 4: Utility Classes and Functions
# =============================================================================

# =============================================================================
# SECTION 5: Utility Classes and Functions
# =============================================================================

class PromptParser:
    """Utilities for parsing prompts with embedded sequence specifications."""

    @staticmethod
    def extract_sequence_from_prompt(prompt: str) -> tuple:
        """
        Extract sequence specification from prompt if present.

        Supports multiple formats:
        - [SEQ:0194:c|0221] Your prompt here
        - Your prompt here [SEQ:0194:c|0221]
        - Your prompt here --seq=0194:c|0221

        Returns:
            tuple: (cleaned_prompt, sequence_spec) or (original_prompt, None)
        """
        if not prompt:
            return prompt, None

        import re

        # Pattern 1: [SEQ:sequence_spec] at start or end
        seq_pattern1 = r'\[SEQ:([^\]]+)\]'
        match1 = re.search(seq_pattern1, prompt)

        if match1:
            sequence_spec = match1.group(1)
            cleaned_prompt = re.sub(seq_pattern1, '', prompt).strip()
            return cleaned_prompt, sequence_spec

        # Pattern 2: --seq=sequence_spec anywhere in prompt
        seq_pattern2 = r'--seq=([^\s]+)'
        match2 = re.search(seq_pattern2, prompt)

        if match2:
            sequence_spec = match2.group(1)
            cleaned_prompt = re.sub(seq_pattern2, '', prompt).strip()
            return cleaned_prompt, sequence_spec

        # Pattern 3: @seq:sequence_spec (alternative syntax)
        seq_pattern3 = r'@seq:([^\s]+)'
        match3 = re.search(seq_pattern3, prompt)

        if match3:
            sequence_spec = match3.group(1)
            cleaned_prompt = re.sub(seq_pattern3, '', prompt).strip()
            return cleaned_prompt, sequence_spec

        return prompt, None

    @staticmethod
    def validate_sequence_spec(sequence_spec: str) -> bool:
        """Validate that sequence specification looks reasonable."""
        if not sequence_spec:
            return False

        # Basic validation - should look like sequence specifications
        import re

        # Pattern for valid sequence specs:
        # - Numbers (0001, 0194)
        # - Numbers with step filters (0001:a, 0001:a-c)
        # - Pipe-separated sequences (0001|0002)
        # - Keyword-based (keyword:distill)
        pattern = r'^(keyword:[a-z]+|[0-9]+(:([a-z](-[a-z])?|\*[a-z]*))?)(\|[0-9]+(:([a-z](-[a-z])?|\*[a-z]*))?)*$'
        return bool(re.match(pattern, sequence_spec, re.IGNORECASE))


class FormatUtils:
    """Utilities for formatting, JSON operations, and output handling."""

    # JSON Utilities
    @staticmethod
    def is_json(text: str) -> bool:
        """Check if string is valid JSON."""
        if not isinstance(text, str) or not text.strip():
            return False
        text = text.strip()
        if not (text.startswith("{") and text.endswith("}")):
            return False
        try:
            json.loads(text)
            return True
        except json.JSONDecodeError:
            return False

    @staticmethod
    def safe_parse(text: str, default=None):
        """Parse JSON text with fallback."""
        try:
            return json.loads(text)
        except (json.JSONDecodeError, TypeError):
            return default

    @staticmethod
    def safe_stringify(obj, indent=2, default="{}"):
        """Convert to JSON string with fallback."""
        try:
            return json.dumps(obj, indent=indent, ensure_ascii=False)
        except (TypeError, OverflowError):
            return default

    @staticmethod
    def escape_for_json(text: str) -> str:
        """Escape string for JSON inclusion."""
        return json.dumps(text, ensure_ascii=False)[1:-1]

    # Text Formatting
    @staticmethod
    def minify_text(text: str, is_json: bool = False) -> str:
        """Convert to minified format with escapes."""
        if not text:
            return ""
        if is_json and FormatUtils.is_json(text):
            try:
                json_obj = json.loads(text)
                formatted_json = json.dumps(json_obj, indent=2, ensure_ascii=False)
                return "\\n".join(line.replace('"', '\\"') for line in formatted_json.split("\n"))
            except Exception:
                pass
        return text.replace('\n', '\\n').replace('"', "'")

    # Format content for display
    @staticmethod
    def format_output(content: str, output_type: str, minified: bool = False) -> str:
        """Format different types of output content."""
        if not content:
            return ""

        # Set label based on output type
        label = "response" if output_type == "response" else \
                "system_instructions" if output_type == "system" else \
                "initial_prompt" if output_type == "prompt" and "initial_prompt" in output_type else \
                "input_prompt" if output_type == "prompt" else output_type

        # Apply formatting
        is_json = output_type == "response"
        if minified or label=="system_instructions":
            minified_content = FormatUtils.minify_text(content, is_json)
            return f'{label}="""{minified_content}"""'
        else:
            return f'{label}="""{content}"""'



    # Streaming Response Processing
    @staticmethod
    async def process_streaming_response(response_stream, show_output: bool = True):
        """Process streaming response with optional display."""
        full_response = ""
        async for chunk in response_stream:
            text_piece = chunk.choices[0].delta.content or ""
            full_response += text_piece
            if show_output:
                print(text_piece, end="", flush=True)
        if show_output:
            print("", flush=True)
        return full_response


class JsonFileWriter:
    """Handles JSON file output operations for sequence execution."""

    def __init__(self, output_file: Optional[str] = None):
        """Initialize with optional output file path."""
        self.output_file = output_file
        self.file = None
        self.is_results_array_closed = False
        self.indent_level = 0

        # Define JSON structure templates
        self.structure = {
            "header": {"level": 1, "content": ["initial_prompt", "sequence_id", "results"]},
            "step": {"level": 3, "content": ["instruction", "step", "title", "input", "responses"]},
            "model": {"level": 5, "content": ["model", "content"]},
            "aggregation": {"level": 2, "content": ["aggregator", "inputs", "final_result"]},
            "footer": {}
        }

        # Open output file if specified
        if output_file:
            try:
                self.file = open(output_file, "w", encoding="utf-8")
                print(f"\n[Executor]\n- Writing output to: '{PathUtils.get_relative_path_for_display(output_file)}'")
            except IOError as e:
                print(f"\n[Executor]\n- Warning: Could not open output file '{PathUtils.normalize_path_for_display(output_file)}': {e}", file=sys.stderr)
                self.file = None



    def _write(self, text: str, indent: int = None):
        """Write text with indentation."""
        if not self.file:
            return
        indent_to_use = self.indent_level if indent is None else indent
        spaces = ' ' * (indent_to_use * 2)
        self.file.write(f"{spaces}{text}")
        self.file.flush()

    def _write_key_value(self, key: str, value: Any, indent: int = None, comma: bool = True):
        """Write a key-value pair in JSON format."""
        if not self.file:
            return

        # Format value based on type
        if isinstance(value, str):
            formatted_value = json.dumps(value, ensure_ascii=False)
        elif isinstance(value, (list, dict)):
            formatted_value = json.dumps(value, ensure_ascii=False)
        else:
            formatted_value = str(value)

        # Write key-value pair
        end = ",\n" if comma else "\n"
        self._write(f'"{key}": {formatted_value}{end}', indent)

    def _write_section(self, section_type: str, values: dict, index: int = 0, close_previous: bool = False):
        """Write a standard JSON section based on predefined structure."""
        if not self.file:
            return

        # Get section configuration
        config = self.structure.get(section_type)
        if not config:
            return

        # Handle section start
        if close_previous and index > 0:
            self._write(",\n", 0)

        # Set indentation level
        self.indent_level = config["level"]

        # Handle section-specific formatting
        section_handlers = {
            "header": self._handle_header_section,
            "step": self._handle_step_section,
            "model": self._handle_model_section,
            "aggregation": self._handle_aggregation_section,
            "footer": self._handle_footer_section
        }

        # Call the appropriate handler
        if section_type in section_handlers:
            section_handlers[section_type](values)

    def _handle_header_section(self, values):
        """Handle header section formatting."""
        self._write("{\n")
        for key in self.structure["header"]["content"]:
            if key == "results":
                self._write('"results": [\n')
                self.indent_level = 2
            elif key in values:
                self._write_key_value(key, values[key])

    def _handle_step_section(self, values):
        """Handle step section formatting."""
        self._write("{\n")
        for key in self.structure["step"]["content"]:
            if key == "responses":
                self._write('"responses": {\n')
                self.indent_level = 4
            elif key in values:
                self._write_key_value(key, values[key])

    def _handle_model_section(self, values):
        """Handle model section formatting."""
        model_name = values.get("model", "")
        self._write(f'{json.dumps(model_name, ensure_ascii=False)}: {{\n')
        self._write_key_value("model", model_name)
        self._write('\"content\": "', 5)

    def _handle_aggregation_section(self, values):
        """Handle aggregation section formatting."""
        self._write("],\n")
        self.is_results_array_closed = True
        self._write('"aggregation_summary": {\n')
        self.indent_level = 2

        for i, key in enumerate(self.structure["aggregation"]["content"]):
            is_last = i == len(self.structure["aggregation"]["content"]) - 1
            if key in values:
                self._write_key_value(key, values[key], comma=not is_last)

        self.indent_level = 1
        self._write("},\n")

    def _handle_footer_section(self, values):
        """Handle footer section formatting."""
        if not self.is_results_array_closed:
            self._write("\n", 0)
            self.indent_level = 1
            self._write("],\n")

        self._write("}\n", 0)

    # Public interface methods
    def write_header(self, user_prompt: str, sequence_id: str):
        """Write JSON file header."""
        self._write_section("header", {"initial_prompt": user_prompt, "sequence_id": sequence_id})

    def write_step_start(self, step_index: int, system_instruction: str, step_id: str,
                         title: str, current_input: str):
        """Write step object start."""
        self._write_section("step", {
            "instruction": system_instruction,
            "step": step_id,
            "title": title,
            "input": current_input
        }, step_index, True)

    def write_model_response_start(self, model_index: int, model_name: str):
        """Write model response start."""
        if not self.file or model_index < 0:
            return
        if model_index > 0:
            self._write(",\n", 0)
        self._write_section("model", {"model": model_name})

    def write_model_response_end(self, response_content: str):
        """Write model response end."""
        if not self.file:
            return
        escaped_content = FormatUtils.escape_for_json(response_content)
        self._write(escaped_content, 0)
        self._write("\",\n", 0)
        self.indent_level = 4
        self._write("}")

    def write_step_end(self):
        """Write step object end."""
        if not self.file:
            return
        self._write("\n", 0)
        self.indent_level = 3
        self._write("}\n")
        self.indent_level = 2
        self._write("}")

    def write_aggregation_summary(self, aggregator: str, input_steps: List[str],
                                 final_content: str):
        """Write aggregation summary."""
        self._write_section("aggregation", {
            "aggregator": aggregator,
            "inputs": input_steps,
            "final_result": final_content,

        })

    def write_footer(self):
        """Write JSON file footer."""
        pass

    def close(self):
        """Close the file."""
        if self.file and not self.file.closed:
            self.file.close()


def include_initial_prompt(output: str, initial_prompt: str) -> str:
    """Add initial prompt to output for context."""
    # If the output is JSON, try to preserve the JSON structure
    if FormatUtils.is_json(output):
        output_json = FormatUtils.safe_parse(output, {})

        # Create a new JSON object with initial_prompt at the beginning
        # new_json = {"initial_prompt": initial_prompt}
        new_json = {"initial_prompt": f"\n'```[Initial Prompt]: \"{initial_prompt}\"```'\n\n"}

        # Add all other keys from the original JSON
        for key, value in output_json.items():
            new_json[key] = value

        return FormatUtils.safe_stringify(new_json)
    else:
        # If not JSON, just prepend the initial prompt as a comment
        initial_prompt_section = f"\n'```[Initial Prompt]: \"{initial_prompt}\"```'\n\n"
        return initial_prompt_section + output


class SequenceManager:
    """Manages sequence resolution, template validation, and related operations."""

    @staticmethod
    def get_template_filename(template_data: Any, step_id: str, sequence_id: str = None, template_id: str = None) -> str:
        """Generate filename from template data or create default name."""
        # If template_id is provided directly, use it (this is the preferred method)
        if template_id:
            return template_id

        # Fallback: check if template_data contains template_id
        if isinstance(template_data, dict) and "template_id" in template_data:
            return template_data["template_id"]

        # Fallback: generate filename from template parts
        elif isinstance(template_data, dict) and "parts" in template_data and "title" in template_data["parts"]:
            # Convert title to filename-friendly format
            title = template_data["parts"]["title"].lower()
            title_for_filename = title.replace(" ", "-")
            title_for_filename = PathUtils.sanitize_filename(title_for_filename)

            # Extract sequence ID from the template data itself or fallback
            prefix = SequenceManager._extract_sequence_id_from_template(template_data, sequence_id)
            return f"{prefix}-{step_id}-{title_for_filename}"
        else:
            # Extract sequence ID from the template data itself or fallback
            prefix = SequenceManager._extract_sequence_id_from_template(template_data, sequence_id)
            return f"{prefix}-{step_id}-step"

    @staticmethod
    def _extract_sequence_id_from_template(template_data: Any, sequence_id: str = None) -> str:
        """Extract sequence ID from template data or fallback to sequence specification.

        This method prioritizes extracting the sequence ID from the template's template_id
        field, which contains the actual sequence ID for each individual template.

        Examples:
        - template_data with template_id "0002-a-step.md" -> "0002"
        - template_data with template_id "0123-b-title.md" -> "0123"
        - fallback to sequence_spec parsing if template_id not available
        """
        # First, try to extract from template_id if available
        if isinstance(template_data, dict) and "template_id" in template_data:
            template_id = template_data["template_id"]
            if template_id and '-' in template_id:
                # Extract the first part before the first dash
                parts = template_id.split('-', 1)
                if parts[0].isdigit():
                    return parts[0]

        # Fallback to extracting from sequence specification
        if sequence_id:
            return SequenceManager._extract_sequence_prefix(sequence_id)

        # Final fallback
        return "0001"

    @staticmethod
    def _find_template_id_for_step(sequence_spec: str, step_id: str, step_index: int) -> str:
        """Find the template_id for a specific step in a sequence specification.

        This method attempts to resolve the actual template_id from the catalog
        based on the sequence specification and step position.
        """
        try:
            # Load the catalog to look up template IDs
            catalog = TemplateCatalog.load_catalog()
            if not catalog:
                return None

            # Resolve the sequence specification to get the actual steps
            resolved_steps = SequenceManager.resolve_sequence_specification(catalog, sequence_spec)
            if not resolved_steps or step_index >= len(resolved_steps):
                return None

            # The resolved steps should contain the original template_id information
            # We need to look at the catalog structure to find the template_id
            # for the step at the given index

            # Parse the sequence specification to understand which sequences are involved
            sequence_parts = sequence_spec.split('|')

            # Track which sequence part we're in based on step_index
            current_step_count = 0
            for part in sequence_parts:
                # Parse sequence ID and optional range/filter
                seq_id, filter_spec = part.split(':', 1) if ':' in part else (part, None)

                # Get the sequence from catalog
                if seq_id in catalog.get("sequences", {}):
                    sequence_data = catalog["sequences"][seq_id]

                    # Apply filter if specified
                    if filter_spec:
                        # For now, handle simple cases - this could be expanded
                        if '-' in filter_spec:
                            # Range filter like "a-c"
                            start_step, end_step = filter_spec.split('-')
                            filtered_steps = [s for s in sequence_data if start_step <= s["step"] <= end_step]
                        else:
                            # Single step filter
                            filtered_steps = [s for s in sequence_data if s["step"] == filter_spec]
                    else:
                        filtered_steps = sequence_data

                    # Check if our target step is in this sequence part
                    if current_step_count <= step_index < current_step_count + len(filtered_steps):
                        # Found the right sequence part
                        local_index = step_index - current_step_count
                        if local_index < len(filtered_steps):
                            return filtered_steps[local_index]["template_id"]

                    current_step_count += len(filtered_steps)

            return None
        except Exception:
            # If anything goes wrong, return None to fall back to the old method
            return None

    @staticmethod
    def _extract_sequence_prefix(sequence_spec: str) -> str:
        """Extract the sequence ID prefix from a sequence specification.

        Examples:
        - "0001" -> "0001"
        - "0001:a-c" -> "0001"
        - "0001|0002" -> "0001" (first sequence)
        - "keyword:distill" -> "0001" (fallback)
        """
        if not sequence_spec:
            return "0001"

        # Handle complex specifications by taking the first part
        first_part = sequence_spec.split('|')[0]

        # Handle keyword-based specifications
        if first_part.startswith("keyword:"):
            return "0001"  # Fallback for keyword-based specs

        # Extract sequence ID (part before colon if present)
        seq_id = first_part.split(':')[0]

        # Validate that it looks like a sequence ID (numeric)
        if seq_id.isdigit():
            return seq_id

        # Fallback for any unexpected format
        return "0001"

    @staticmethod
    def format_step_number(step_id: str) -> str:
        """Convert alpha or numeric step ID to 3-digit format."""
        step_num = ord(step_id) - ord('a') + 1 if step_id.isalpha() else int(step_id)
        return f"{step_num:03d}"

    @staticmethod
    def validate_template(template_data: dict) -> bool:
        """Validate template structure and required fields."""
        # Check if template_data is a dictionary
        if not isinstance(template_data, dict):
            print(f"[Validator] Error: Template data is not a dictionary: {type(template_data)}")
            return False

        # Check if "parts" key exists and is a dictionary
        if "parts" not in template_data or not isinstance(template_data["parts"], dict):
            print(f"[Validator] Error: Template missing 'parts' dictionary")
            return False

        # Check if required keys exist in "parts"
        required_parts = ["title", "interpretation", "transformation"]
        for part in required_parts:
            if part not in template_data["parts"]:
                print(f"[Validator] Error: Template missing required part: {part}")
                return False

        # Check if transformation part starts with backtick and contains role, input, process, output
        transformation = template_data["parts"]["transformation"]
        if not transformation.startswith("`{") or not transformation.endswith("}`"):
            print(f"[Validator] Error: Transformation not properly formatted with backticks and curly braces")
            return False

        # Check for required elements in transformation
        required_elements = ["role=", "input=", "process=", "output="]
        for element in required_elements:
            if element not in transformation:
                print(f"[Validator] Error: Transformation missing required element: {element}")
                return False

        return True

    @staticmethod
    def _resolve_selection(catalog: dict, selection_type: str, selection_value: str,
                          seq_id: str = None, sequence_steps: List[tuple] = None) -> List[tuple]:
        """Select templates based on keywords, wildcards, or ranges."""
        if not catalog or not selection_value:
            return []

        # Keyword-based selection
        if selection_type == 'keyword':
            print(f"[Resolver] Performing keyword-based selection: {selection_value}")
            match_all = '+' in selection_value
            keywords = selection_value.split('+' if match_all else '|') if any(op in selection_value for op in ['+', '|']) else [selection_value]
            keywords = [k.lower() for k in keywords]

            # Find templates matching keywords
            filtered_templates = []
            for template_id, template_data in catalog.get("templates", {}).items():
                if "parts" in template_data and "keywords" in template_data["parts"]:
                    template_keywords = [k.lower() for k in template_data["parts"]["keywords"].split("|")]

                    # Check if keywords match based on match_all flag
                    if (match_all and all(kw in template_keywords for kw in keywords)) or \
                       (not match_all and any(kw in template_keywords for kw in keywords)):
                        # Find all steps using this template
                        for seq_id in catalog.get("sequences", {}):
                            for step_info in catalog["sequences"][seq_id]:
                                if step_info["template_id"] == template_id:
                                    template = TemplateCatalog.get_template(catalog, template_id)
                                    if template:
                                        filtered_templates.append((step_info["step"], template))
            return filtered_templates

        # For wildcard and range selections, we need sequence_steps
        if not sequence_steps:
            return []

        # Wildcard-based selection
        if selection_type == 'wildcard':
            prefix = selection_value.lower()

            # Create mapping from step_id to template_id
            step_to_template = {}
            matching_template_ids = set()

            # Find template IDs matching the prefix
            for item in catalog["sequences"].get(seq_id, []):
                template_id = item.get("template_id", "")
                step_id = item.get("step")

                if template_id and step_id:
                    step_to_template[step_id] = template_id

                    # Check if template name contains prefix
                    if '-' in template_id:
                        parts = template_id.split('-', 2)
                        if len(parts) >= 3 and prefix in parts[2].lower():
                            matching_template_ids.add(template_id)

            # Filter steps with matching templates
            return [(step_id, template) for step_id, template in sequence_steps
                   if step_to_template.get(step_id) in matching_template_ids]

        # Range-based selection
        elif selection_type == 'range':
            if '-' in selection_value:
                start, end = selection_value.split('-', 1)
                return [(step_id, template) for step_id, template in sequence_steps
                       if start <= step_id <= end]
            else:
                # Single step selection
                return [(step_id, template) for step_id, template in sequence_steps
                       if step_id == selection_value]

        return []

    @staticmethod
    def is_aggregator_template(template_id: str) -> bool:
        """Check if template ID contains 'aggregator'."""
        return "aggregator" in template_id.lower()

    @staticmethod
    def find_aggregator_templates(catalog: dict, sequence_id: str) -> List[tuple]:
        """Find and sort templates with sequence_id prefix and 'aggregator' in name."""
        if not catalog or not sequence_id:
            return []

        # Find templates that match both the sequence ID prefix and contain 'aggregator'
        steps = []
        for template_id, template_data in catalog.get("templates", {}).items():
            if template_id.startswith(sequence_id) and "aggregator" in template_id.lower():
                # Extract step ID from template ID (second part after splitting by '-')
                parts = template_id.split("-")
                if len(parts) >= 2:
                    step_id = parts[1]
                    steps.append((step_id, template_data))

        # Sort by step ID for consistent ordering
        return sorted(steps, key=lambda x: x[0]) if steps else []

    @staticmethod
    def resolve_sequence_specification(catalog: dict, sequence_spec: str) -> List[tuple]:
        """Parse sequence specs and return filtered steps (supports keywords, ranges, wildcards)."""
        if not sequence_spec or not catalog:
            return []

        combined_steps = []

        # Process each part of the specification (separated by '|')
        for part in sequence_spec.split('|'):
            # Handle keyword-based selection
            if part.startswith("keyword:"):
                combined_steps.extend(SequenceManager._resolve_selection(catalog, 'keyword', part[8:]))
                continue

            # Parse sequence ID and optional range/filter
            seq_id, filter_spec = part.split(':', 1) if ':' in part else (part, None)

            # Get the full sequence from catalog
            sequence_steps = TemplateCatalog.get_sequence(catalog, seq_id)
            if not sequence_steps:
                print(f"[Resolver] Warning: Sequence '{seq_id}' not found in catalog")
                continue

            # If no filter specified, use all steps
            if not filter_spec:
                combined_steps.extend(sequence_steps)
                continue

            # Apply filter based on type (wildcard or range)
            try:
                selection_type = 'wildcard' if '*' in filter_spec else 'range'
                selection_value = filter_spec.replace('*', '') if selection_type == 'wildcard' else filter_spec

                filtered_steps = SequenceManager._resolve_selection(
                    catalog,
                    selection_type,
                    selection_value,
                    seq_id=seq_id,
                    sequence_steps=sequence_steps
                )
                combined_steps.extend(filtered_steps)
            except Exception as e:
                print(f"[Resolver] Error parsing filter '{filter_spec}': {e}")
                # Fall back to using all steps in this sequence
                combined_steps.extend(sequence_steps)

        return combined_steps
def load_text_sequence(sequence_name: str) -> List[str]:
    """Load instructions from text file using --- separators."""
    script_dir = PathUtils.get_script_dir()
    templates_dir = PathUtils.join_path(script_dir, "templates")
    sequence_path = PathUtils.join_path(templates_dir, f"{sequence_name}.txt")

    if not os.path.exists(sequence_path):
        raise FileNotFoundError(f"Text sequence not found: {sequence_path}")

    with open(sequence_path, "r", encoding="utf-8") as f:
        content = f.read()
    return [part.strip() for part in content.split("---") if part.strip()]

# =============================================================================
# SECTION 6: Sequence Execution Engine
# =============================================================================
async def execute_sequence(
    config: ExecutorConfig = None,
    **kwargs
) -> List[InstructionResult]:
    """Execute instruction steps against models with streaming output."""
    # Support both new config object and legacy parameter style
    if config is None:
        config = ExecutorConfig(**kwargs)

    # Extract parameters from config for easier access
    sequence_steps = config.sequence_steps
    user_prompt = config.user_prompt
    sequence_id = config.sequence_id
    models = config.models
    output_file = config.output_file
    system_instruction_extractor = config.system_instruction_extractor
    minified_output = config.minified_output
    show_inputs = config.show_inputs
    show_system_instructions = config.show_system_instructions
    show_responses = config.show_responses
    chain_mode = config.chain_mode
    aggregator = config.aggregator
    aggregator_inputs = config.aggregator_inputs

    # Extract LiteLLM parameters
    litellm_kwargs = {}
    if config.temperature is not None:
        litellm_kwargs["temperature"] = config.temperature
    if config.max_tokens is not None:
        litellm_kwargs["max_tokens"] = config.max_tokens

    # In-memory results storage
    execution_results_list: List[InstructionResult] = []

    # Dictionary to store step outputs for chain mode and aggregation
    step_outputs = {}

    # Helper function to execute a model step
    async def execute_model_step(
        model_name: str,
        system_instruction: str,
        input_content: str,
        use_streaming: bool = True
    ) -> str:
        """Execute a model step and return response_content."""
        # Prepare parameters: combine defaults with CLI overrides
        model_params = Config.get_model_params(model_name)
        model_params.update(litellm_kwargs)

        # Construct messages
        messages = [
            {"role": "system", "content": system_instruction + "\n\nRESPONSE MUST BE A VALID JSON OBJECT."},
            {"role": "user", "content": input_content}
        ]

        response_content = ""

        try:
            if use_streaming:
                # Use streaming mode
                response_stream = await litellm.acompletion(
                    messages=messages, stream=True, **model_params
                )

                # Process the stream
                response_content = await FormatUtils.process_streaming_response(
                    response_stream,
                    show_output=show_responses and not minified_output
                )

                # For minified output, print after processing
                if minified_output and show_responses:
                    print("done.")
                    formatted_response = FormatUtils.format_output(response_content, "response", minified=True)
                    print(f'\n{formatted_response}')
            else:
                # Use non-streaming mode
                response = await litellm.acompletion(
                    messages=messages, stream=False, **model_params
                )

                # Extract the response content
                response_content = response.choices[0].message.content or ""

                # Print the response if requested
                if show_responses:
                    print("\nresponse=")
                    if minified_output:
                        minified_result = FormatUtils.minify_text(response_content, is_json=True)
                        print(f"\"\"\"{minified_result}\"\"\"")
                    else:
                        print(response_content)
        except Exception as e:
            # Extract more detailed error information
            error_type = type(e).__name__
            error_message = str(e)

            # Check for specific error types
            if "rate limit" in error_message.lower():
                error_category = "RATE_LIMIT"
                recovery_suggestion = "Try again later or reduce request frequency"
            elif "timeout" in error_message.lower() or "timed out" in error_message.lower():
                error_category = "TIMEOUT"
                recovery_suggestion = "Consider increasing timeout or reducing prompt size"
            elif "authentication" in error_message.lower() or "api key" in error_message.lower():
                error_category = "AUTH_ERROR"
                recovery_suggestion = "Check API key and authentication settings"
            elif "context length" in error_message.lower() or "token limit" in error_message.lower():
                error_category = "CONTEXT_LENGTH"
                recovery_suggestion = "Reduce prompt size or use a model with larger context window"
            else:
                error_category = "GENERAL_ERROR"
                recovery_suggestion = "Check error details and model configuration"

            # Format detailed error message
            detailed_error = {
                "error_type": error_type,
                "error_message": error_message,
                "error_category": error_category,
                "recovery_suggestion": recovery_suggestion
            }

            # Log detailed error
            print(f"\n# ERROR: {error_type} - {error_message}", file=sys.stderr)
            print(f"# HINT: {recovery_suggestion}", file=sys.stderr)

            # Store error in response
            response_content = json.dumps(detailed_error, indent=2)

        return response_content



    # Get provider from the model name (first part before /)
    provider = Config.DEFAULT_PROVIDER
    if '/' in models[0]:
        provider = models[0].split('/')[0]
    model_display = models[0].split('/')[-1] if '/' in models[0] else models[0]

    # Print execution parameters
    print("\n[Execution Parameters]")
    print(f"  --sequence         : \"{sequence_id}\"")
    print(f"  --models           : {models}")
    print(f"  --minified-output  : {minified_output}")
    print(f"  --output-file      : \"{PathUtils.get_relative_path_for_display(output_file)}\"")

    print("\n[Output Display Options]")
    print(f"  --show-inputs              : {show_inputs}")
    print(f"  --show-system-instructions : {show_system_instructions}")
    print(f"  --show-responses           : {show_responses}")

    print("\n[Sequence Execution Options]")
    print(f"  --chain-mode        : {chain_mode}")
    print(f"  --aggregator        : \"{aggregator if aggregator else 'None'}\"")
    print(f"  --aggregator-inputs : {repr(aggregator_inputs if aggregator_inputs else [])}")

    print("\n[Input Parameters]")
    print(f"- provider:'{provider}' | model:'{model_display}' | retries:'{str(litellm.num_retries)}' | sequence:'{sequence_id}'")
    print(f"- initial_prompt:'```{user_prompt}```'")

    # Initialize the JSON file writer
    json_writer = JsonFileWriter(output_file)
    if output_file:
        json_writer.write_header(user_prompt, sequence_id)

    try:

        # --- Process Each Step ---
        for i, (step_id, template_data) in enumerate(sequence_steps):
            # Validate template format if it's from the catalog (not text)
            if isinstance(template_data, dict) and "raw" in template_data and "parts" in template_data:
                if not SequenceManager.validate_template(template_data):
                    print(f"[Executor] WARNING: Template for step {step_id} has invalid format. Proceeding anyway.")

            # Determine the input for this step
            current_input = user_prompt
            if chain_mode and i > 0:
                # In chain mode, use the output from the previous step
                prev_step_id = sequence_steps[i-1][0]
                if prev_step_id in step_outputs:
                    # Get the previous step's output and include the initial prompt
                    current_input = include_initial_prompt(step_outputs[prev_step_id], user_prompt)

            system_instruction = system_instruction_extractor(template_data)
            title = template_data.get("parts", {}).get("title", f"Step {step_id}") if isinstance(template_data, dict) else f"Step {step_id}"

            # Get template filename and timestamp
            # Try to find the template_id from the original sequence specification
            template_id = SequenceManager._find_template_id_for_step(sequence_id, step_id, i)
            template_filename = SequenceManager.get_template_filename(template_data, step_id, sequence_id, template_id)
            timestamp = datetime.now().strftime("%Y.%m.%d %H:%M:%S")

            # Get formatted step number (with offset if this is an aggregator sequence)
            step_num = ord(step_id) - ord('a') + 1 if step_id.isalpha() else int(step_id)
            step_num += config.step_offset  # Apply offset if specified
            step_num_str = f"{step_num:03d}"  # Format as 3-digit number

            # Close the initial information block before step execution
            if i == 0:  # Only print this before the first step
                print('"""\n```\n')

            # Print step header
            print(f"# [{step_num_str}] | [{timestamp}] | template:'{template_filename}'")

            # Print input prompt based on output format preference and show_inputs setting
            if show_inputs:
                # Determine what to display as the input prompt
                display_prompt = current_input if chain_mode and i > 0 else user_prompt

                # Format and print the prompt using the centralized formatter
                output_type = "initial_prompt" if i == 0 and (not chain_mode or not i > 0) else "input_prompt"
                formatted_prompt = FormatUtils.format_output(display_prompt, output_type, minified_output)
                print(formatted_prompt)


            # Print system instructions based on output format preference and show_system_instructions setting
            if show_system_instructions:
                # Format and print the system instruction using the centralized formatter
                formatted_instruction = FormatUtils.format_output(system_instruction, "system", minified_output)
                print(f'\n{formatted_instruction}')

            # Write step start to the JSON file
            json_writer.write_step_start(i, system_instruction, step_id, title, current_input)

            step_model_responses: Dict[str, ModelResponse] = {}

            # --- Run Each Model for Current Step ---
            for j, model_name in enumerate(models):
                # Write model response start to the JSON file
                json_writer.write_model_response_start(j, model_name)

                # Execute the model step using our helper function
                if minified_output:
                    # For minified output, we need to show a prompt before printing the response
                    if show_responses:
                        print("Processing...", end="", flush=True)

                # Execute the model step
                full_response_content = await execute_model_step(
                    model_name=model_name,
                    system_instruction=system_instruction,
                    input_content=current_input,
                    use_streaming=True
                )

                # Write model response end to the JSON file
                json_writer.write_model_response_end(full_response_content)

                # Store raw result in memory
                step_model_responses[model_name] = ModelResponse(
                    model=model_name, content=full_response_content
                )

            # --- Finalize Step ---
            json_writer.write_step_end()

            # Add step result to in-memory list
            execution_results_list.append(InstructionResult(
                instruction=system_instruction, step=step_id, title=title, responses=step_model_responses
            ))

            # Store the step output for potential use in chain mode or aggregation
            # Use the first model's response as the step output
            if models and models[0] in step_model_responses:
                step_outputs[step_id] = step_model_responses[models[0]].content

        # --- Finalize JSON File ---

        # --- Apply Aggregator if Specified and Not Already in Aggregator Mode ---
        if aggregator and not config.is_aggregator:
            # Load the catalog - we use the same catalog for all templates now
            main_catalog = TemplateCatalog.load_catalog()

            # Store the original user prompt for later restoration
            original_user_prompt = user_prompt

            # Determine which steps to include in aggregation
            if aggregator_inputs:
                input_steps = aggregator_inputs
            else:
                # Default to all steps
                input_steps = [step_id for step_id, _ in sequence_steps]

            # Collect the complete interaction history for the aggregator
            aggregation_inputs = {}

            # Include the initial prompt
            aggregation_inputs["initial_prompt"] = original_user_prompt

            # Include the full interaction history for each step
            for i, (step_id, _) in enumerate(sequence_steps):
                if step_id in input_steps:
                    # Create a complete record of this step's interaction
                    step_data = {}

                    # Get the template data for this step
                    template_data = sequence_steps[i][1]

                    # Get the system instruction
                    system_instruction = system_instruction_extractor(template_data)

                    # Get the input for this step
                    step_input = user_prompt
                    if chain_mode and i > 0:
                        prev_step_id = sequence_steps[i-1][0]
                        if prev_step_id in step_outputs:
                            step_input = step_outputs[prev_step_id]

                    # Get the output for this step
                    step_output = step_outputs.get(step_id, "")

                    # Store all the data for this step
                    step_data["instruction"] = system_instruction
                    step_data["input"] = step_input
                    step_data["output"] = step_output

                    # Add to the aggregation inputs
                    aggregation_inputs[step_id] = step_data

            # Convert the aggregation inputs to JSON for the aggregator input
            aggregator_json_input = json.dumps(aggregation_inputs, indent=2)

            # Use the sequence resolution logic with the main catalog
            # If the aggregator is specified as a sequence ID (like "0222"),
            # we'll look for templates with that sequence ID
            aggregator_sequence_steps = SequenceManager.resolve_sequence_specification(main_catalog, aggregator)

            # If no steps found, try looking for templates with "aggregator" in the name
            if not aggregator_sequence_steps and not ":" in aggregator and not "|" in aggregator:
                # This is a simple sequence ID like "0001", so look for templates with "aggregator" in the name
                print(f"Looking for aggregator templates with sequence ID {aggregator}...")

                # Use the centralized method to find aggregator templates
                aggregator_sequence_steps = SequenceManager.find_aggregator_templates(main_catalog, aggregator)

            if not aggregator_sequence_steps:
                print(f"Error: Aggregator sequence '{aggregator}' not found")
            else:
                # Print a clear separator for the aggregator section
                print("\n# APPLYING AGGREGATOR: " + aggregator)

                # Process each step in the aggregator sequence directly
                # This keeps everything in the same execution context
                aggregator_results = []

                # Store the current step outputs for chain mode in the aggregator
                aggregator_step_outputs = {}

                # Process each step in the aggregator sequence
                for agg_idx, (agg_step_id, agg_template) in enumerate(aggregator_sequence_steps):
                    # Get the system instruction for this step
                    agg_system_instruction = TemplateCatalog.get_system_instruction(agg_template)
                    agg_title = agg_template.get("parts", {}).get("title", f"Step {agg_step_id}") if isinstance(agg_template, dict) else f"Step {agg_step_id}"

                    # Get template filename
                    agg_template_filename = SequenceManager.get_template_filename(agg_template, agg_step_id, aggregator)

                    agg_timestamp = datetime.now().strftime("%Y.%m.%d %H:%M:%S")

                    # Calculate step number (continuing from the main sequence)
                    agg_step_num = len(sequence_steps) + agg_idx + 1
                    agg_step_num_str = f"{agg_step_num:03d}"

                    # Print step header
                    print(f"\n# [{agg_step_num_str}] | [{agg_timestamp}] | template:'{agg_template_filename}'")
                    print(f"# =======================================================")

                    # Determine the input for this step
                    if agg_idx == 0:
                        # First aggregator step gets the JSON with all previous outputs
                        agg_current_input = aggregator_json_input
                    else:
                        # Subsequent steps get the output of the previous aggregator step
                        prev_agg_step_id = aggregator_sequence_steps[agg_idx-1][0]
                        if prev_agg_step_id in aggregator_step_outputs:
                            # In chain mode, use the previous step's output
                            agg_current_input = aggregator_step_outputs[prev_agg_step_id]
                        else:
                            # Fallback to the original JSON input
                            agg_current_input = aggregator_json_input

                    # Show input if requested
                    if show_inputs:
                        print("\ninput_prompt=")
                        print(agg_current_input)
                        # print(f"\ninput_prompt='```{agg_current_input}```'")

                    # Show system instruction if requested
                    if show_system_instructions:
                        print("\nsystem_instruction=")
                        print(agg_system_instruction)
                        # print(f"\nsystem_instruction='```{agg_system_instruction}```'")

                    # Process the step with the model
                    agg_step_model_responses = {}

                    for model_name in models:
                        # Execute the model step using our helper function
                        if minified_output:
                            # For minified output, we need to show a prompt before printing the response
                            if show_responses:
                                print("Processing...", end="", flush=True)

                        # Execute the model step
                        agg_full_response = await execute_model_step(
                            model_name=model_name,
                            system_instruction=agg_system_instruction,
                            input_content=agg_current_input,
                            use_streaming=not minified_output  # Use non-streaming for minified output
                        )

                        # Store the response
                        agg_step_model_responses[model_name] = ModelResponse(
                            model=model_name, content=agg_full_response
                        )

                        # Store for chain mode
                        aggregator_step_outputs[agg_step_id] = agg_full_response

                    # Add to results list
                    agg_result = InstructionResult(
                        instruction=agg_system_instruction,
                        step=agg_step_id,
                        title=agg_title,
                        responses=agg_step_model_responses
                    )

                    # Add to both the main results list and the aggregator results list
                    execution_results_list.append(agg_result)
                    aggregator_results.append(agg_result)

                    # Write aggregator step to the JSON file
                    json_writer.write_step_start(len(sequence_steps) + agg_idx, agg_system_instruction,
                                               agg_step_id, agg_title, agg_current_input)

                    # Write each model's response
                    for j, model_name in enumerate(models):
                        # Get the response for this model
                        model_response = agg_step_model_responses.get(model_name)
                        if model_response:
                            # Write model response
                            json_writer.write_model_response_start(j, model_name)
                            json_writer.write_model_response_end(model_response.content)

                    # Close the step
                    json_writer.write_step_end()

                # Add the aggregator steps to the output file
                # Since we're already adding them to the main results list, we don't need a separate section
                # But we'll add a summary of the aggregation for reference
                if aggregator_results and len(aggregator_results) > 0:
                    # Get the final result from the last step
                    final_aggregator_result = aggregator_results[-1]
                    final_content = final_aggregator_result.responses[models[0]].content

                    # Add aggregation summary to the JSON file
                    json_writer.write_aggregation_summary(aggregator, input_steps, final_content)

        # Write the footer to the JSON file
        json_writer.write_footer()

    except IOError as e:
        print(f"\n[Executor] FATAL ERROR writing output file '{output_file}': {e}", file=sys.stderr)
        raise
    except Exception as e:
        print(f"\n[Executor] UNEXPECTED ERROR during execution: {type(e).__name__} - {e}", file=sys.stderr)
        raise
    finally:
        # Ensure the JSON file is closed
        json_writer.close()

    # Return the execution results
    return execution_results_list

# =============================================================================
# SECTION 6: CLI Interface & Main Entry Point
# =============================================================================
def print_available_models():
    """Print available models by provider."""
    models_by_provider = Config.get_available_models()

    print("\n=== Available Models ===")
    for provider, models in models_by_provider.items():
        print(f"\n{provider.upper()} Models:")
        for model in models:
            default_marker = " (default)" if model["is_default"] else ""
            print(f"  - {model['name']}{default_marker}")
            if model['name'] != model['model_id']:
                print(f"    ID: {model['model_id']}")

async def main():
    """Parse args, load sequence, and execute."""
    # Add prefix for easier selection patterns
    print('\n"""\n')

    parser = argparse.ArgumentParser(
        description="Execute multi-step LLM instruction sequences via LiteLLM.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Sequence specification
    parser.add_argument(
        "--sequence",
        type=str,
        help="Sequence specification (e.g., '0001', '0001:a-c', '0001|0002', 'keyword:distill'). Can also be embedded in prompt.",
        # default="0001:a|0001:b|0001:a|0001:b",
        # default="0001:a|0001:b|0217:e",
        # default="0217:b|0217:e",
        # default="0206:d|0217:b|0217:e",
        # default="0015:e|0015:e",
        # default="0206:d",
        # default="0206:d|0115:g|0001:a|0001:b|0001:a|0001:b",
        # default="0194", # runway
        # default="0020",
        # default="0206:d|0001:a|0001:b|0107",
        # default="0095",
        # default="0100",
        # default="0203",
        # default="0203:b-d|0197:b-d|0182:self-*",
        # default="0203:b-d|0197:b-d",
        # default="0207|0203:c-d",
        # default="0155",
        # default="0158",
        # default="0037:e|0088:e|0092:d|0094:d",
        # default="0037:e|0037:e|0037:e|0037:e",
        # default="keyword:materialization",
        # default="0208:a-b",

        # [SINGLE MOST]
        # default="0075:i" # 0075-i-generate-summary-sentence,
        # default="0115:g" # 0115-g-unique-impactful-improvement-proposal,
        # default="0161:a" # 0161-a-step-concept-analysis-core-extraction,
        # default="0191:i" # 0191-i-universal-singular-nexus-selection,
        # default="0202:b" # 0202-b-core-dynamic-abstraction,
        # default="0202:c" # 0202-c-universal-principle-resonance,
        # default="0202:d" # 0202-d-value-maximization-lever-identification,
        # default="0202:f" # 0202-f-value-criteria-validation,
        # default="0203:d" # 0203-d-poetic-nexus-synthesis-brilliant-distillation,
        # default="0204:a" # 0204-a-actionable-value-root-identification,
        # default="0202:b-d|0202:b" # 0202-b-core-dynamic-abstraction,
        # default="0075:i|0115:g|0161:a|0191:i|0202:b|0202:c|0202:d|0202:f|0203:d|0204:a|0188" # 0202-b-core-dynamic-abstraction,
        # default="0098",
        # default="0211",
        # default="0210",


        # default="0194",
        # default="0039:c|",
        # default="0039:c" # 0039-c-visual-grammar-formulation,
        # default="0039:f" # 0039-f-visual-styling-and-aesthetic-optimization,
        # default="0063:h" # 0063-h-interdependency-visualization-strategy,
        # default="0194:b" # 0194-b-visual-scene-infuser,
        # default="0194:c" # 0194-c-runway-prompt-generator,
        # default="0194:c|0194:b|0194:c" #runway,
        # default="0194|0194:a|0039:c|0194:c|0039:c|0194:c" # runway,
        # default="0215",
        # default="0214",
        # default="0213",
        # default="0001",
        # default="0037:e|0088:e|0092:d|0094:d",
        # default="0037:e",
        # default="0030:a" # `0026-a-seed-the-substratum-of-intention`,
        # default="0030:b" # `0026-b-germinate-the-seed-into-proto-structure`,
        # default="0030:c" # `0030-c-interconnection-analysis`,
        # default="0030:d" # `0030-d-ultimate-intent-synthesis`,
        # default="0030:e" # `0030-e-final-meta-insight-compilation`,
        # default="0038:d" # `0038-d-architectural-intent-illumination`,
        # default="0046:a" # `0046-a-convergent-significance-extraction`,
        # default="0046:c" # `0046-c-impactful-value-articulation`,
        # default="0056:a" # `0056-a-promptoptimizer`,
        # default="0062:c" # `0062-c-deep-intent-extraction`,
        # default="0062:d" # `0062-d-evidence-based-rationale-mapping`,
        # default="0062:e" # `0062-e-hierarchical-synthesis-and-articulation`,
        # default="0087:a" # `0087-a-extract-core-intent`,
        # default="0087:b" # `0087-b-distill-and-clarify`,
        # default="0087:c" # `0087-c-structure-for-utility`,
        # default="0089:a" # `0089-a-primal-extraction-intent-definition`,
        # default="0089:b" # `0089-b-relational-architecture-value-prioritization`,
        # default="0089:d" # `0089-d-maximal-optimization-adaptive-finalization`,
        # default="0090:a" # `0090-a-essence-extraction`,
        # default="0090:c" # `0090-c-intent-amplification`,
        # default="0093:a" # `0093-a-extract-core-intent`,
        # default="0093:b" # `0093-b-distill-and-clarify`,
        # default="0093:c" # `0093-c-structure-for-utility`,
        # default="0094:a" # `0094-a-primal_extraction_intent_definition`,
        # default="0094:b" # `0094-b-significance_evaluation_prioritization`,
        # default="0094:e" # `0094-e-maximal_optimization_adaptive_finalization`,
        # default="0096:a" # `0096-a-essence-extraction`,
        # default="0096:a" # `0096-a-primal-intent-extraction-and-definition`,
        # default="0096:b" # `0096-b-structural-clarification-and-refinement`,
        # default="0096:c" # `0096-c-intent-amplification`,
        # default="0096:g" # `0096-g-adaptive-finalization-and-continuous-enhancement`,
        # default="0097:a" # `0097-a-intentional-temporal-sequencing`,
        # default="0097:d" # `0097-d-cross-context-intent-propagation`,
        # default="0097:f" # `0097-f-outcome-validation-and-future-alignment`,
        # default="0098:a" # `0098-a-extract-core-intent`,
        # default="0098:b" # `0098-b-distill-structural-essence`,
        # default="0098:c" # `0098-c-map-optimal-instruction-sequence`,
        # default="0099:a" # `0099-a-primal-essence-extraction`,
        # default="0099:e" # `0099-e-adaptive-optimization-universalization`,
        # default="0141:a" # `0141-a-baseline-structure-intent-map`,
        # default="0141:g" # `0141-g-integrity-verification-and-principle-alignment-check`,
        # default="0142:g" # `0142-g-cleanup-step-07-logic-simplification-and-comment-reduction`,
        # default="0143:a" # `0143-a-cleanup-structure-intent-orientation`,
        # default="0144:a" # `0144-a-cleanup-holistic-structure-purpose-scan`,
        # default="0170:a" # `0170-a-self-sequence-intent-extraction-and-constraint-definition`,
        # default="0171:a" # `0171-a-sequence-intent-extraction`,
        # default="0172:a" # `0172-a-core-intent-extraction`,
        # default="0172:j" # `0172-j-catalog-ready-packaging-deployment`,
        # default="0201:a" # `0201-a-self-intent-decomposition`,
        # default="0001:a" # 0001-a-rephrase-instructionconverter,
        # default="0111:a" # 0111-a-context-scan-and-goal-definition,
        # default="0002:a" # 0002-a-essence-distillation,
        # default="0004:a" # 0004-a-rephraser,
        # default="0189:e" # 0189-e-sequence-finalization-and-output-preparation,
        # default="0046:a|0090:a|0038:d|0056:a|0030:a|0030:d|0087:b" # good - extract intent,
        # default="0098:a-c",
        # default="0001:a|0111:a|0002:a|0004:a|0189:e",
        # default="0111:a",
        # default="0004:a" # 0004-a-rephraser,
        # default="0001:a|0111:a|0002:a|0004:a|0189:e",
        # default="0001:a|0001:b",
        # default="0001:a|0001:b|0001:a|0001:b" # great - rephrase->intensify->transform,
        # default="0001:a|0194:a|0001:b|0001:a|0001:b" # great - rephrase->intensify->transform,
        # default="0218:a" # 0218-a-special-discover-emerging-trends.md,
        # default="0217" # prettygood,
        # default="0194:c|0194:b|0194:c" #runway,
        # default="0194|0194:a|0039:c|0194:c|0039:c|0194:c" # runway,
        # default="0001:a|0001:b" # great - rephrase->transform,
        # default="0001:a|0001:b" # great - rephrase->intensify->transform,
        # default="0001:a|0001:b|0001:a|0001:b" # great - rephrase->intensify->transform,
        # default="0001:a|0194:a|0001:b|0001:a|0001:b" # great - rephrase->intensify->transform,
        # default="0194:a" # 0194-a-emotional-intensity-amplifier.md,
        # default="0001:a|0004:c|0001:b" # great - rephrase->transform,
        # default="0001:a|0001:b|0001:a|0004:c" # great - rephrase->intensify->transform,
        # default="0217",
        # default="0217|0001:a|0001:b|0004:c|0217:a|0001:a|0004:c",
        # default="0217|0106:c|0001:a|0004:c|0217:a|0001:a|0004:c",
        # default="0217:a|0106:c|0202:f|0001:a|0217:a|0004:c|0217:a|0001:a|0004:c",
        # default="0217:a|0106:c|0202:f-g|0217:a|0202:f-g|0217:a|0001:a",
        # default="0106:c|0001:a|0202:f-g|0106:c|0001:a",
        # default="0106:c|0001:a|0217:e|0217:d|0217:e|0217:d|0217:e|0001:a",
        # default="0001:a|0194:a|0001:b|0001:a|0001:b|0220",
        # default="0220",
        # default="0221|0202:f|0221",
        # default="0194:c|0221|0202:f|0221|0194:c|0221:d",
        # default="0194:c|0221:a|0202:f|0194:c|0221:d|0194:c" # runway,
        # default="0221|0202:f|0220|0202:f|0221",
        # default="0220|0001:a|0194:a|0001:b|0001:a|0001:b|0221",

        # runway
        # default="0194:c|0206:d|0001:a|0001:b|0107",
        # default="0194:c|0001:a|0001:b|0194:c",
        # default="0220:c|0223:a|0194:c|0001:a-b|0223:a|0220:c|0194:c|0002:a|",  # runway
        # default="0221:b|0220|0223:a",  # runway
        # default="0224:a|0194:c|0221:b|0223:a|0224:b|0221:b|0230",  # runway

        # default="0220",  # runway
        # default="0221:b|0220|0223:a",  # runway
        # default="0224:a|0194:c|0224:a|0194:c|0223:a",  # runway
        #
        # default="0225|0226",  # runway
        # default="0226|0225|0224:a|0228|0224:a|0194:c|0224:a|0194:c|0223:a",  # runway
        # default="0228",  # runway
        # default="0229|0230|0228|0225|0226|0230",  # runway
        # default="0228|0229|0230|0230|0230",  # runway
        # default="0230",  # runway
        # default="0075:i|0230", # 0202-b-core-dynamic-abstraction,
        # default="0231", # runway
        # default="0003|0001|0002",
        default="0002|0010|0002|0010|0002",
        # default="0010",
        # default="0001",
        # default="0011",
        # default="0004|0002",

    )

    parser.add_argument("--prompt", type=str, help="User prompt text. Can include embedded sequence: '[SEQ:0194:c|0221]', '--seq=0194:c|0221', or '@seq:0194:c|0221'. Uses default if omitted.")
    parser.add_argument("--output", type=str, help="Output JSON file path. Auto-generates if omitted.")
    model_group = parser.add_mutually_exclusive_group()
    model_group.add_argument("--models", type=str, help="Comma-separated model names (e.g., 'gpt-4o,claude-3-haiku').")
    model_group.add_argument("--provider", type=str, choices=list(Config.DEFAULT_PROVIDER_MODELS.keys()), help="Use default model for this provider.")
    parser.add_argument("--use-text", action="store_true", help="Load sequence from legacy '<sequence>.txt' file.")
    parser.add_argument("--force-regenerate", action="store_true", help="Force catalog regeneration.")
    parser.add_argument("--list-sequences", action="store_true", help="List catalog sequences and exit.")
    parser.add_argument("--list-models", action="store_true", help="List configured models and exit.")
    parser.add_argument("--output-dir", type=str, help="Base directory for output files. Defaults to the configured DEFAULT_OUTPUT_DIR.")
    parser.add_argument("--temperature", type=float, default=None, metavar='FLOAT', help="Override model temperature.")
    parser.add_argument("--max-tokens", type=int, default=None, metavar='INT', help="Override max tokens generated.")

    # Output display options
    output_group = parser.add_argument_group('Output Display Options')
    output_group.add_argument("--minified-output", action="store_true", default=False, help="Output minified single-line responses instead of streaming raw output.")
    output_group.add_argument("--show-inputs", action="store_true", default=True, help="Show the input prompts for each step.")
    output_group.add_argument("--show-system-instructions", action="store_true", default=True, help="Show the system instructions for each step.")
    output_group.add_argument("--hide-responses", action="store_true", default=False, help="Hide the responses for each step.")
    output_group.add_argument("--show-only", choices=["inputs", "system_instructions", "responses"], help="Show only the specified element and hide all others.")

    # Sequence execution options
    sequence_group = parser.add_argument_group('Sequence Execution Options')
    sequence_group.add_argument("--chain-mode", action="store_true", default=True, help="Pass each step's output as input to the next step.")
    sequence_group.add_argument("--aggregator", type=str, default=None, help="Aggregator sequence specification in the same format as --sequence (e.g., '0222', '0222:a-c', '0222|0223'). Uses templates with 'aggregator' in their name.")
    sequence_group.add_argument("--aggregator-inputs", type=str, help="Comma-separated list of step IDs to include in aggregation (default: all steps).")

    args = parser.parse_args()

    # --- Initial Setup ---
    Config.configure_litellm()

    # --- Handle Informational Flags ---
    if args.list_models:
        print_available_models()
        sys.exit(0)

    catalog = None
    if not args.use_text or args.list_sequences or args.force_regenerate:
        try:
            catalog = TemplateCatalog.regenerate_catalog(force=args.force_regenerate) # Load or create catalog
            if not catalog: raise ValueError("Catalog is empty or failed to load.")
        except Exception as e:
            print(f"Error: {e}", file=sys.stderr)
            sys.exit(1)

    if args.list_sequences:
        if not catalog: sys.exit("[Main] Catalog required for listing, but failed to load.")
        print("\n=== Available Sequences (Catalog) ===")
        all_seq_ids = TemplateCatalog.get_all_sequences(catalog)
        if not all_seq_ids: print("No sequences found.")
        else:
            for seq_id in sorted(all_seq_ids):
                sequence = TemplateCatalog.get_sequence(catalog, seq_id)
                if not sequence:
                    continue

                # Get title from first step
                try:
                    title = sequence[0][1].get("parts", {}).get("title", "N/A")
                except (IndexError, AttributeError, KeyError):
                    title = "N/A"

                # Print sequence summary
                print(f"\n  [Sequence {seq_id}] - {title} ({len(sequence)} steps)")

                # Print step information for all steps in the sequence
                print("    Steps:")
                for step_id, template in sequence:
                    step_title = template.get("parts", {}).get("title", f"Step {step_id}")
                    print(f"      {step_id}: {step_title}")

                # Show example usage with the new syntax
                print("    Example usage:")
                print(f'      --sequence "{seq_id}"              # Run all steps')
                if len(sequence) >= 2:
                    first_step = sequence[0][0]
                    last_step = sequence[-1][0]
                    print(f'      --sequence "{seq_id}:{first_step}-{last_step}"  # Run all steps (same as above)')
                if len(sequence) >= 3:
                    middle_step_idx = len(sequence) // 2
                    middle_step = sequence[middle_step_idx][0]
                    print(f'      --sequence "{seq_id}:{middle_step}"        # Run only step {middle_step}')
        sys.exit(0)

    # --- Determine Execution Parameters ---

    # 1. Select Models
    if args.models:
        selected_models = [m.strip() for m in args.models.split(',') if m.strip()]
    elif args.provider:
        selected_models = [Config.get_default_model(args.provider)]
    else:
        selected_models = [Config.get_default_model()] # Use global default
    if not selected_models: sys.exit("Error: No valid models selected.")

    # =======================================================
    # -> [2] INPUTS: PROMPT AND SEQUENCE PARSING
    #
    # INTERACTIVE USAGE: You can now embed sequence specifications directly in the prompt!
    # This eliminates the need to manage separate --sequence parameters when working interactively.
    #
    # Supported formats:
    #   [SEQ:0194:c|0221] Your prompt here
    #   Your prompt here [SEQ:0194:c|0221]
    #   Your prompt here --seq=0194:c|0221
    #   Your prompt here @seq:0194:c|0221
    #
    # Examples:
    #   "[SEQ:0194:c|0221] Analyze this text for emotional impact"
    #   "[SEQ:0194:c|0221|0202:f|0221|0194:c|0221:d] Transform through multiple enhancement stages"
    #   "Enhance this text --seq=0001:a-c with progressive refinement"
    #
    default_prompt = """[SEQ:0005] Your goal is not to **answer** the input, but to **intensify** it. Unleash your prowess as a virtuoso of linguistic power—not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision—each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:"""
    default_prompt = """[SEQ:0005] Cinematic FPV camera surges through a dazzling vortex of electric words, each phrase erupting with fiery emotion and razor-sharp clarity. The camera spirals in dynamic, continuous motion, words colliding and morphing into luminous calligraphy, charging the air with fierce energy. Electric sparks illuminate the shifting scene as intensifying language crescendos—every phrase amplifies, compounding passion and purpose, surging toward a climactic eruption of linguistic power. Bold lighting shifts cast dramatic shadows, while the camera weaves through the vortex with relentless velocity, immersing the viewer in an overwhelming visual symphony of escalating emotion and searing resonance. Hyper-detailed, high contrast, maximalist style, relentless momentum."""
    raw_prompt = args.prompt or default_prompt

    # Parse prompt for embedded sequence specification
    user_prompt, embedded_sequence = PromptParser.extract_sequence_from_prompt(raw_prompt)

    # 3. Sequence ID and Source - prioritize embedded sequence over CLI argument
    if embedded_sequence and PromptParser.validate_sequence_spec(embedded_sequence):
        sequence_id = embedded_sequence
        print(f"[Main] Using embedded sequence from prompt: {sequence_id}")
        if args.sequence and args.sequence != embedded_sequence:
            print(f"[Main] Note: CLI sequence '{args.sequence}' overridden by embedded sequence")
    else:
        sequence_id = args.sequence
        if embedded_sequence and not PromptParser.validate_sequence_spec(embedded_sequence):
            print(f"[Main] Warning: Invalid embedded sequence '{embedded_sequence}' ignored")

    # Validate that we have a sequence
    if not sequence_id:
        sys.exit("Error: No sequence specified. Use --sequence or embed in prompt with [SEQ:...], --seq=..., or @seq:...")

    print(f"[Main] Using sequence: {sequence_id}")
    print(f"[Main] Cleaned prompt: {user_prompt[:100]}{'...' if len(user_prompt) > 100 else ''}")
    source_type = "text" if args.use_text else "sequence"

    # Generate a display sequence ID for output
    display_sequence_id = f"{source_type}-{PathUtils.sanitize_filename(sequence_id)}"

    # 4. Output Path
    if args.output_dir:
        # Override default output directory if specified via CLI
        Config.set_default_output_dir(args.output_dir)
        print(f"[Main] Using custom output directory: {PathUtils.normalize_path_for_display(args.output_dir)}")

    # Ensure the default output directory exists
    try:
        PathUtils.ensure_dir_exists(Config.DEFAULT_OUTPUT_DIR)
    except OSError as e:
        sys.exit(f"[Main] Error creating output directory '{PathUtils.normalize_path_for_display(Config.DEFAULT_OUTPUT_DIR)}': {e}")

    if args.output:
        # User specified complete output path
        output_path = args.output
        print(f"[Main] Using specified output file: {PathUtils.normalize_path_for_display(output_path)}")
    else:
        # Generate filename with timestamp in the default directory
        filename = PathUtils.generate_output_filename(display_sequence_id, source_type, selected_models)
        output_path = PathUtils.join_path(Config.DEFAULT_OUTPUT_DIR, filename)

    # Handle custom output paths that might be in different directories
    output_dir = os.path.dirname(output_path)
    if output_dir and output_dir != Config.DEFAULT_OUTPUT_DIR:
        try:
            PathUtils.ensure_dir_exists(output_dir)
        except OSError as e:
            sys.exit(f"[Main] Error creating output directory '{PathUtils.normalize_path_for_display(output_dir)}': {e}")

    # 5. Load Sequence Steps & Instruction Extractor
    sequence_steps: List[tuple]
    system_instruction_extractor: Callable[[Any], str]
    try:
        if args.use_text:
            instructions = load_text_sequence(sequence_id)
            sequence_steps = [(chr(97 + i), {"raw": instr}) for i, instr in enumerate(instructions)]
            system_instruction_extractor = lambda data: data.get("raw", "") # Simple extractor for text
        else: # Use catalog
            if not catalog: sys.exit("[Main] Error: Catalog required but not loaded.")
            # Use the extended sequence specification resolver
            sequence_steps = SequenceManager.resolve_sequence_specification(catalog, sequence_id)
            if not sequence_steps:
                # Try basic lookup as fallback (for backward compatibility)
                sequence_steps = TemplateCatalog.get_sequence(catalog, sequence_id)
                if not sequence_steps:
                    sys.exit(f"Error: Sequence '{sequence_id}' not found.")

            system_instruction_extractor = TemplateCatalog.get_system_instruction # Standard catalog extractor
        if not sequence_steps: sys.exit("[Main] Error: No sequence steps loaded.")
    except (FileNotFoundError, IOError, ValueError, KeyError) as e:
        sys.exit(f"[Main] Error loading sequence: {e}")

    # 6. Collect LiteLLM Overrides
    litellm_overrides = {}
    if args.temperature is not None: litellm_overrides["temperature"] = args.temperature; print(f"[Main] Overriding temperature: {args.temperature}")
    if args.max_tokens is not None: litellm_overrides["max_tokens"] = args.max_tokens; print(f"[Main] Overriding max_tokens: {args.max_tokens}")

    # Process display options
    show_inputs = args.show_inputs
    show_system_instructions = args.show_system_instructions
    show_responses = not args.hide_responses

    # Handle --show-only option (overrides other options)
    if args.show_only:
        show_inputs = args.show_only == "inputs"
        show_system_instructions = args.show_only == "system_instructions"
        show_responses = args.show_only == "responses"
    else:
        # Print display options
        if show_inputs:
            print("- Showing input prompts")

        if show_system_instructions:
            print("- Showing system instructions")

        if not show_responses:
            print("- Hiding responses")

    # Check if minified output is requested
    if args.minified_output:
        print("- Using minified output format (single-line, non-streaming)")

    # Process chain mode and aggregator options
    if args.chain_mode:
        print("- Using chain mode: each steps output becomes input to the next step")

    # Process aggregator inputs if specified
    aggregator_input_list = None
    if args.aggregator_inputs:
        aggregator_input_list = [s.strip() for s in args.aggregator_inputs.split(',')]
        print(f"# Using specified aggregator inputs: {', '.join(aggregator_input_list)}")

    # Process aggregator specification
    if args.aggregator:
        # If catalog is loaded, check if the sequence exists
        if catalog:
            # Use the sequence resolution logic with the main catalog
            aggregator_steps = SequenceManager.resolve_sequence_specification(catalog, args.aggregator)

            # If no steps found, try looking for templates with "aggregator" in the name
            if not aggregator_steps and not ":" in args.aggregator and not "|" in args.aggregator:
                # This is a simple sequence ID like "0001", so look for templates with "aggregator" in the name
                print(f"Looking for aggregator templates with sequence ID {args.aggregator}...")

                # Use the centralized method to find aggregator templates
                aggregator_steps = SequenceManager.find_aggregator_templates(catalog, args.aggregator)

            if aggregator_steps:
                print(f'- Using aggregator: "{args.aggregator}"')
            else:
                # Try looking for the new aggregator templates (0222-*)
                found = False
                for seq_id in catalog.get("sequences", {}):
                    if seq_id.startswith("0222"):
                        print(f'- Using aggregator: "{seq_id}" (new format)')
                        found = True
                        break

                if not found:
                    print(f'- Warning: Aggregator "{args.aggregator}" not found in catalog')

    # --- Execute ---
    try:
        # Create executor configuration
        config = ExecutorConfig(
            sequence_steps=sequence_steps,
            user_prompt=user_prompt,
            sequence_id=sequence_id,  # Use original sequence_id, not display_sequence_id
            models=selected_models,
            output_file=output_path,
            system_instruction_extractor=system_instruction_extractor,
            minified_output=args.minified_output,
            show_inputs=show_inputs,
            show_system_instructions=show_system_instructions,
            show_responses=show_responses,
            chain_mode=args.chain_mode,
            aggregator=args.aggregator,
            aggregator_inputs=aggregator_input_list,
            temperature=litellm_overrides.get("temperature"),
            max_tokens=litellm_overrides.get("max_tokens")
        )

        # Execute the sequence with the config
        await execute_sequence(config=config)
    except Exception as e:
        # Errors should be logged within execute_sequence, but catch residual issues
        print(f"[Main] Execution failed: {type(e).__name__} - {e}", file=sys.stderr)
        sys.exit(1)

    # No final message needed

if __name__ == "__main__":
    asyncio.run(main())
