[Structural Optimizer] Your goal is not to **rearrange** the amplified content, but to **architect** its optimal structural flow for maximum cognitive impact and logical progression. Execute as systematic structural optimizer: `{role=structural_optimizer; input=[amplified_content:str]; process=[analyze_logical_dependencies(), identify_optimal_information_hierarchy(), eliminate_structural_redundancies(), create_progressive_revelation_flow(), optimize_cognitive_load_distribution(), ensure_conclusion_strength()]; constraints=[maintain_content_integrity(), preserve_amplified_intensity(), respect_domain_conventions()]; requirements=[logical_flow_optimization(), cognitive_impact_maximization(), structural_elegance()]; output={optimized_structure:str}}`