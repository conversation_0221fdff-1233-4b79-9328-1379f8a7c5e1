import os
import sys
import json
import uuid
import time
import asyncio
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
from dotenv import load_dotenv

import litellm

# Basic configuration
load_dotenv()
litellm.drop_params = True
litellm.num_retries = 3
litellm.request_timeout = 120
litellm.set_verbose = False

class ChainNode:
    def __init__(
        self,
        system_instruction: str,
        input_prompt: str,
        model: str = "gpt-4.1",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        parent: Optional['ChainNode'] = None,
        name: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.system_instruction = system_instruction
        self.input_prompt = input_prompt
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.parent = parent
        self.children: List['ChainNode'] = []
        self.response: Optional[str] = None
        self.node_id = str(uuid.uuid4())[:8]
        self.execution_time: Optional[float] = None
        self.name = name or f"Node-{self.node_id}"
        self.metadata = metadata or {}
        self.created_at = datetime.now()
        self.execution_count = 0

    def then(
        self,
        system_instruction: str,
        input_prompt: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        name: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> 'ChainNode':
        """Add a next step in the chain, using this node's response as input if no input is provided."""
        actual_input = input_prompt if input_prompt is not None else "{response}"
        
        child = ChainNode(
            system_instruction=system_instruction,
            input_prompt=actual_input,
            model=model or self.model,
            temperature=temperature or self.temperature,
            max_tokens=max_tokens or self.max_tokens,
            parent=self,
            name=name,
            metadata=metadata
        )
        self.children.append(child)
        return child

    def branch(
        self,
        system_instruction: str,
        input_prompt: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        name: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> 'ChainNode':
        """Create a branch from this node, identical to then() but with a different name for clarity."""
        return self.then(
            system_instruction=system_instruction,
            input_prompt=input_prompt,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            name=name,
            metadata=metadata
        )

    async def execute(self, stream: bool = True) -> str:
        """Execute this node by querying the LLM and storing the response."""
        start_time = time.time()
        self.execution_count += 1
        
        # Process template variables in the input prompt
        processed_input = self._process_template_variables()
        
        # Print execution information if streaming
        if stream:
            print(f"\n--- Executing: {self.name} (ID: {self.node_id}) ---")
            print(f"Model: {self.model}, Temperature: {self.temperature}")
            print(f"System: {self.system_instruction[:100]}..." if len(self.system_instruction) > 100 else f"System: {self.system_instruction}")
            print(f"Input: {processed_input[:100]}..." if len(processed_input) > 100 else f"Input: {processed_input}")
            print("\nGenerating response...")
        
        # Query the LLM
        self.response = await self._query_llm(
            system_instruction=self.system_instruction,
            input_prompt=processed_input,
            model=self.model,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            stream=stream
        )
        
        self.execution_time = time.time() - start_time
        
        if stream:
            print(f"\nCompleted in {self.execution_time:.2f}s")
        
        return self.response

    async def execute_chain(self, stream: bool = True) -> Dict[str, Optional[str]]:
        """Execute this node and all its children sequentially."""
        # Execute this node first
        await self.execute(stream=stream)
        results = {self.node_id: self.response}
        
        # Execute children sequentially
        for child in self.children:
            child_results = await child.execute_chain(stream=stream)
            results.update(child_results)
        
        return results

    def _process_template_variables(self) -> str:
        """Process template variables in the input prompt."""
        processed_input = self.input_prompt
        
        # Replace {response} with this node's parent's response
        if self.parent and self.parent.response and "{response}" in processed_input:
            processed_input = processed_input.replace("{response}", self.parent.response)
        
        # Replace {node:NODE_ID.response} with the response from the specified node
        node_refs = re.findall(r'\{node:([a-zA-Z0-9]+)\.response\}', processed_input)
        if node_refs:
            root = self._get_root()
            for node_id in node_refs:
                node = root._find_node_by_id(node_id)
                if node and node.response:
                    processed_input = processed_input.replace(f"{{node:{node_id}.response}}", node.response)
        
        return processed_input

    async def _query_llm(
        self,
        system_instruction: str,
        input_prompt: str,
        model: str,
        temperature: float,
        max_tokens: Optional[int] = None,
        stream: bool = False
    ) -> str:
        """Query the LLM using litellm."""
        messages = [
            {"role": "system", "content": system_instruction},
            {"role": "user", "content": input_prompt}
        ]
        
        params = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
        }
        
        if max_tokens is not None:
            params["max_tokens"] = max_tokens
        
        try:
            if stream:
                print("Generating response...", end="", flush=True)
            
            response = await litellm.acompletion(**params)
            
            # Extract content from response
            content = ""
            if isinstance(response, dict) and 'choices' in response and response['choices']:
                content = response['choices'][0]['message'].get('content', '')
            
            if not content and hasattr(response, 'choices'):
                try:
                    choices = getattr(response, 'choices')
                    if choices and len(choices) > 0:
                        first_choice = choices[0]
                        if hasattr(first_choice, 'message'):
                            message = getattr(first_choice, 'message')
                            if hasattr(message, 'content'):
                                content = getattr(message, 'content')
                except:
                    pass
            
            if not content:
                try:
                    content = str(response)
                except:
                    content = "Failed to extract content from response"
            
            if stream:
                print("\r" + " " * 20 + "\r", end="")
                print(content)
            
            return content.strip() if content else ""
            
        except Exception as e:
            error_type = type(e).__name__
            print(f"\nERROR: LiteLLM API Error ({model}): {error_type} - {str(e)}", file=sys.stderr)
            return f"ERROR: LiteLLM call failed - {error_type}"

    def _get_root(self) -> 'ChainNode':
        """Get the root node of the tree."""
        current = self
        while current.parent:
            current = current.parent
        return current

    def _find_node_by_id(self, node_id: str) -> Optional['ChainNode']:
        """Find a node by its ID."""
        if self.node_id == node_id:
            return self
        
        for child in self.children:
            result = child._find_node_by_id(node_id)
            if result:
                return result
        
        return None

    def print_chain(self, indent: int = 0, max_content_length: int = 50) -> None:
        """Print the chain structure."""
        prefix = "  " * indent
        branch = "└─ " if indent > 0 else ""
        
        print(f"{prefix}{branch}Node: {self.name} (ID: {self.node_id}) [Runs: {self.execution_count}]")
        
        content_prefix = "  " * (indent + 1)
        print(f"{content_prefix}System: {self.system_instruction[:max_content_length]}..." 
              if len(self.system_instruction) > max_content_length 
              else f"{content_prefix}System: {self.system_instruction}")
        
        print(f"{content_prefix}Input: {self.input_prompt[:max_content_length]}..." 
              if len(self.input_prompt) > max_content_length 
              else f"{content_prefix}Input: {self.input_prompt}")
        
        print(f"{content_prefix}Model: {self.model}, Temp: {self.temperature}")
        
        if self.response is not None:
            print(f"{content_prefix}Response: {self.response[:max_content_length]}..." 
                  if len(self.response) > max_content_length 
                  else f"{content_prefix}Response: {self.response}")
            
            if self.execution_time:
                print(f"{content_prefix}Time: {self.execution_time:.2f}s")
        
        for child in self.children:
            print(f"{content_prefix}│")
            child.print_chain(indent + 1, max_content_length)

    def save_to_file(self, file_path: str) -> None:
        """Save the chain to a JSON file."""
        chain_data = self._to_dict()
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(chain_data, f, indent=2, ensure_ascii=False)

    def _to_dict(self) -> Dict[str, Any]:
        """Convert the node to a dictionary."""
        result = {
            "node_id": self.node_id,
            "name": self.name,
            "system_instruction": self.system_instruction,
            "input_prompt": self.input_prompt,
            "model": self.model,
            "temperature": self.temperature,
            "created_at": self.created_at.isoformat(),
            "execution_count": self.execution_count,
        }
        
        if self.max_tokens is not None:
            result["max_tokens"] = self.max_tokens
        
        if self.metadata:
            result["metadata"] = self.metadata
        
        if self.response is not None:
            result["response"] = self.response
            result["execution_time"] = self.execution_time
        
        if self.children:
            result["children"] = [child._to_dict() for child in self.children]
        
        return result

# Example usage
async def main():
    print(f"\n--- Sequential LLM Chain Demo ---")
    
    # Define the initial user input
    user_input = """Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation."""
    
    # Create the root node - starting point of our chain
    root = ChainNode(
        system_instruction="You are a converter that transforms user input into a structured format. Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.",
        input_prompt=user_input,
        model="gpt-4.1",
        temperature=0.15,
        name="Initial Conversion"
    )
    
    # Execute the first node
    await root.execute()
    
    # Chain: Take the first response and enhance it
    enhancer = root.then(
        system_instruction="You are an enhancer that improves the quality and clarity of text.",
        # No input_prompt means it will use the parent's response
        temperature=0.15,
        name="Enhancement"
    )
    
    # Execute the enhancer
    await enhancer.execute()
    
    # Chain: Evaluate the enhancement by comparing original and enhanced versions
    evaluator = root.then(
        system_instruction="You are an evaluator that identifies issues in text transformations.",
        input_prompt=f"original: `{root.response}`\nrefined: `{enhancer.response}`",
        temperature=0.15,
        name="Evaluation"
    )
    
    # Execute the evaluator
    await evaluator.execute()
    
    # Chain: Finalize by addressing the identified issues
    finalizer = root.then(
        system_instruction="You are a finalizer that addresses identified issues.",
        input_prompt=f"Original: `{root.response}`\nEnhanced: `{enhancer.response}`\nIssues to address: `{evaluator.response}`",
        temperature=0.15,
        name="Finalization"
    )
    
    # Execute the finalizer
    await finalizer.execute()
    
    # Chain: Convert the final result back to a desired format
    converter = finalizer.then(
        system_instruction="You are a converter that transforms text into a final polished format.",
        # No input_prompt means it will use the parent's response
        temperature=0.15,
        name="Final Conversion"
    )
    
    # Execute the converter
    await converter.execute()
    
    # Print the chain structure
    print("\n--- Sequential Processing Chain ---")
    root.print_chain()
    
    # Save the results
    output_file = "sequential_chain_results.json"
    root.save_to_file(output_file)
    print(f"\nSequential chain saved to {output_file}")
    
    print("\n--- Sequential LLM Chain Demo Complete ---")

if __name__ == "__main__":
    import re  # Import here to avoid circular import issues
    asyncio.run(main())
