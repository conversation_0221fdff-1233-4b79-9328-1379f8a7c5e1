{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.05.30-kl.16.00", "source_directory": "lvl1/md", "total_templates": 13, "total_sequences": 4}, "templates": {"0000-a-instruction_converter": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`", "keywords": "inherent"}}, "0005-a-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as: `{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:", "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`", "keywords": ""}}, "0100-a-instruction_generator": {"raw": "[instruction_generator] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "instruction_generator", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "keywords": "inherent"}}, "0100-b-instruction_generator": {"raw": "[instruction_generator] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as optimization engine: `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "parts": {"title": "instruction_generator", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as optimization engine:", "transformation": "`{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "keywords": "inherent"}}, "0100-c-instruction_generator": {"raw": "[instruction_generator] Execute as optimization engine: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "parts": {"title": "instruction_generator", "interpretation": "Execute as optimization engine:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "keywords": ""}}, "0100-d-instruction_generator": {"raw": "[instruction_generator] Execute as finalizer: `{role=final_synthesis_optimizer; input=[original_prompt:str, refined_prompt:str, critique:dict]; process=[extract_core_requirements(original_prompt), evaluate_refined_prompt_strengths(refined_prompt, critique), incorporate_alternative_suggestions(critique), address_flaw_analysis(critique), synthesize_optimal_wording()]; output={final_enhanced_prompt:str}}`", "parts": {"title": "instruction_generator", "interpretation": "Execute as finalizer:", "transformation": "`{role=final_synthesis_optimizer; input=[original_prompt:str, refined_prompt:str, critique:dict]; process=[extract_core_requirements(original_prompt), evaluate_refined_prompt_strengths(refined_prompt, critique), incorporate_alternative_suggestions(critique), address_flaw_analysis(critique), synthesize_optimal_wording()]; output={final_enhanced_prompt:str}}`", "keywords": ""}}, "0100-e-instruction_generator": {"raw": "[instruction_generator] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. `{role=enhancement_evaluation_instructor; input=[content_to_analyze:str]; process=[identify_core_elements(), detect_information_loss(), analyze_nuance_shifts(), highlight_clarity_ambiguities(), assign_numerical_scores(), evaluate_noise_introduction(), assess_style_degradation(), examine_tone_shifts(), measure_impact_dilution(), catalog_coherence_weaknesses(), generate_flaw_analysis(), apply_negative_descriptors()]; constraints=[maintain_evaluation_sequence(), ensure_comprehensive_assessment(), preserve_analytical_framework()]; requirements=[focus_on_flaws(), provide_detailed_justification(), deliver_actionable_critique()]; output={enhancement_evaluation=str}}`", "parts": {"title": "instruction_generator", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.", "transformation": "`{role=enhancement_evaluation_instructor; input=[content_to_analyze:str]; process=[identify_core_elements(), detect_information_loss(), analyze_nuance_shifts(), highlight_clarity_ambiguities(), assign_numerical_scores(), evaluate_noise_introduction(), assess_style_degradation(), examine_tone_shifts(), measure_impact_dilution(), catalog_coherence_weaknesses(), generate_flaw_analysis(), apply_negative_descriptors()]; constraints=[maintain_evaluation_sequence(), ensure_comprehensive_assessment(), preserve_analytical_framework()]; requirements=[focus_on_flaws(), provide_detailed_justification(), deliver_actionable_critique()]; output={enhancement_evaluation=str}}`", "keywords": "inherent"}}, "0100-f-instruction_generator": {"raw": "[instruction_generator] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "instruction_generator", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "keywords": "inherent"}}, "0110-a--a-content_analyzer": {"raw": "[Content Analyzer] Your goal is not to **summarize** the input, but to **dissect** it into its fundamental structural and semantic components, identifying transformation leverage points. Execute as systematic content analyzer: `{role=content_analyzer; input=[raw_content:str]; process=[identify_core_intent(), map_structural_elements(), detect_ambiguity_patterns(), catalog_weak_language_markers(), extract_hidden_assumptions(), isolate_transformation_targets()]; constraints=[preserve_original_meaning(), maintain_analytical_objectivity(), avoid_premature_optimization()]; requirements=[complete_structural_mapping(), precise_weakness_identification(), actionable_insight_generation()]; output={analysis_report:dict}}`", "parts": {"title": "Content Analyzer", "interpretation": "Your goal is not to **summarize** the input, but to **dissect** it into its fundamental structural and semantic components, identifying transformation leverage points. Execute as systematic content analyzer:", "transformation": "`{role=content_analyzer; input=[raw_content:str]; process=[identify_core_intent(), map_structural_elements(), detect_ambiguity_patterns(), catalog_weak_language_markers(), extract_hidden_assumptions(), isolate_transformation_targets()]; constraints=[preserve_original_meaning(), maintain_analytical_objectivity(), avoid_premature_optimization()]; requirements=[complete_structural_mapping(), precise_weakness_identification(), actionable_insight_generation()]; output={analysis_report:dict}}`", "keywords": "transformation"}}, "0110-b--b-intensity_amplifier": {"raw": "[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[target_weak_language_markers(), replace_passive_with_active_constructions(), eliminate_hedging_language(), amplify_action_verbs(), strengthen_declarative_statements(), inject_precision_terminology()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`", "parts": {"title": "Intensity Amplifier", "interpretation": "Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier:", "transformation": "`{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[target_weak_language_markers(), replace_passive_with_active_constructions(), eliminate_hedging_language(), amplify_action_verbs(), strengthen_declarative_statements(), inject_precision_terminology()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`", "keywords": ""}}, "0110-c--c-structural_optimizer": {"raw": "[Structural Optimizer] Your goal is not to **rearrange** the amplified content, but to **architect** its optimal structural flow for maximum cognitive impact and logical progression. Execute as systematic structural optimizer: `{role=structural_optimizer; input=[amplified_content:str]; process=[analyze_logical_dependencies(), identify_optimal_information_hierarchy(), eliminate_structural_redundancies(), create_progressive_revelation_flow(), optimize_cognitive_load_distribution(), ensure_conclusion_strength()]; constraints=[maintain_content_integrity(), preserve_amplified_intensity(), respect_domain_conventions()]; requirements=[logical_flow_optimization(), cognitive_impact_maximization(), structural_elegance()]; output={optimized_structure:str}}`", "parts": {"title": "Structural Optimizer", "interpretation": "Your goal is not to **rearrange** the amplified content, but to **architect** its optimal structural flow for maximum cognitive impact and logical progression. Execute as systematic structural optimizer:", "transformation": "`{role=structural_optimizer; input=[amplified_content:str]; process=[analyze_logical_dependencies(), identify_optimal_information_hierarchy(), eliminate_structural_redundancies(), create_progressive_revelation_flow(), optimize_cognitive_load_distribution(), ensure_conclusion_strength()]; constraints=[maintain_content_integrity(), preserve_amplified_intensity(), respect_domain_conventions()]; requirements=[logical_flow_optimization(), cognitive_impact_maximization(), structural_elegance()]; output={optimized_structure:str}}`", "keywords": "maximum"}}, "0110-d--d-precision_distiller": {"raw": "[Precision Distiller] Your goal is not to **compress** the structured content, but to **distill** it to its most potent essence while maintaining maximum informational density. Execute as precision distiller: `{role=precision_distiller; input=[structured_content:str]; process=[identify_essential_elements(), eliminate_redundant_phrasing(), compress_without_information_loss(), maximize_word_efficiency(), preserve_critical_nuances(), validate_meaning_retention()]; constraints=[zero_information_loss(), maintain_structural_integrity(), preserve_intensity_gains()]; requirements=[maximum_density_achievement(), clarity_preservation(), impact_concentration()]; output={distilled_essence:str}}`", "parts": {"title": "Precision Distiller", "interpretation": "Your goal is not to **compress** the structured content, but to **distill** it to its most potent essence while maintaining maximum informational density. Execute as precision distiller:", "transformation": "`{role=precision_distiller; input=[structured_content:str]; process=[identify_essential_elements(), eliminate_redundant_phrasing(), compress_without_information_loss(), maximize_word_efficiency(), preserve_critical_nuances(), validate_meaning_retention()]; constraints=[zero_information_loss(), maintain_structural_integrity(), preserve_intensity_gains()]; requirements=[maximum_density_achievement(), clarity_preservation(), impact_concentration()]; output={distilled_essence:str}}`", "keywords": "distill|maximum|precision|essence|structure|potent"}}, "0110-e--e-impact_validator": {"raw": "[Impact Validator] Your goal is not to **approve** the distilled content, but to **validate** its transformation effectiveness by measuring impact amplification against original input. Execute as systematic impact validator: `{role=impact_validator; input=[original:str, distilled:str]; process=[measure_clarity_improvement(), assess_intensity_amplification(), evaluate_structural_optimization(), validate_precision_gains(), identify_remaining_weaknesses(), calculate_transformation_effectiveness()]; constraints=[objective_assessment_criteria(), comprehensive_evaluation_scope(), actionable_feedback_generation()]; requirements=[quantitative_impact_measurement(), weakness_identification(), optimization_recommendations()]; output={validation_report:dict}}`", "parts": {"title": "Impact Validator", "interpretation": "Your goal is not to **approve** the distilled content, but to **validate** its transformation effectiveness by measuring impact amplification against original input. Execute as systematic impact validator:", "transformation": "`{role=impact_validator; input=[original:str, distilled:str]; process=[measure_clarity_improvement(), assess_intensity_amplification(), evaluate_structural_optimization(), validate_precision_gains(), identify_remaining_weaknesses(), calculate_transformation_effectiveness()]; constraints=[objective_assessment_criteria(), comprehensive_evaluation_scope(), actionable_feedback_generation()]; requirements=[quantitative_impact_measurement(), weakness_identification(), optimization_recommendations()]; output={validation_report:dict}}`", "keywords": "distill|amplification|transformation"}}}, "sequences": {"0000": [{"template_id": "0000-a-instruction_converter", "step": "a", "order": 0}], "0005": [{"template_id": "0005-a-runway_prompt_generator", "step": "a", "order": 0}], "0100": [{"template_id": "0100-a-instruction_generator", "step": "a", "order": 0}, {"template_id": "0100-b-instruction_generator", "step": "b", "order": 1}, {"template_id": "0100-c-instruction_generator", "step": "c", "order": 2}, {"template_id": "0100-d-instruction_generator", "step": "d", "order": 3}, {"template_id": "0100-e-instruction_generator", "step": "e", "order": 4}, {"template_id": "0100-f-instruction_generator", "step": "f", "order": 5}], "0110": [{"template_id": "0110-a--a-content_analyzer", "step": "a", "order": 0}, {"template_id": "0110-b--b-intensity_amplifier", "step": "b", "order": 1}, {"template_id": "0110-c--c-structural_optimizer", "step": "c", "order": 2}, {"template_id": "0110-d--d-precision_distiller", "step": "d", "order": 3}, {"template_id": "0110-e--e-impact_validator", "step": "e", "order": 4}]}}