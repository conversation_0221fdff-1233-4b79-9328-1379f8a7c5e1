# =============================================================================
# SECTION 1: Core Imports & Environment Setup
# =============================================================================
import os
import sys
import re
import glob
import json
import time
import uuid
import asyncio # Added for async operations
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Union, Optional
from dotenv import load_dotenv
from loguru import logger

# --- LiteLLM Integration ---
import litellm

# Ensure UTF-8 encoding for standard output and error streams.
if hasattr(sys.stdout, "reconfigure"):
    try:
        sys.stdout.reconfigure(encoding="utf-8", errors="replace")
    except Exception as e:
        pass # Ignore if reconfiguration fails
if hasattr(sys.stderr, "reconfigure"):
    try:
        sys.stderr.reconfigure(encoding="utf-8", errors="replace")
    except Exception as e:
        pass # Ignore if reconfiguration fails

# =============================================================================
# SECTION 2: LiteLLM Configuration
# =============================================================================
# Basic LiteLLM setup (can be expanded as needed)
litellm.drop_params = True      # Prevent errors from unsupported parameters across models
litellm.num_retries = 3         # Retry failed API calls
litellm.request_timeout = 120   # Set API request timeout (seconds)
litellm.set_verbose = False     # Reduce LiteLLM's console output (set True for debugging)
# litellm.callbacks = []        # Optional: Disable default callbacks if not needed

load_dotenv()
print("[Config] LiteLLM Initialized (Basic Setup)")
print(f"- Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s")

# =============================================================================
# SECTION 3: Helper Function using LiteLLM
# =============================================================================
# Updated to use LiteLLM and be asynchronous
async def query_llm(
    system_instruction: str,
    input_prompt: str,
    # model: str = "gpt-4.1",
    model: str = "claude-4",
    temperature: float = 0.7,
    max_tokens: Optional[int] = None # Make max_tokens optional
) -> str:
    """
    Queries a Large Language Model using the LiteLLM library.

    Args:
        system_instruction: The system prompt or instruction.
        input_prompt: The user's input prompt.
        model: The LiteLLM model identifier string.
        temperature: The sampling temperature.
        max_tokens: The maximum number of tokens to generate (optional).

    Returns:
        The content of the LLM's response as a string.
    """
    messages = [
        {"role": "system", "content": system_instruction},
        {"role": "user", "content": input_prompt}
    ]

    # Prepare parameters for LiteLLM, only include max_tokens if provided
    params = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
    }
    if max_tokens is not None:
        params["max_tokens"] = max_tokens

    try:
        # Use litellm.acompletion for asynchronous call
        response = await litellm.acompletion(**params)

        # Accessing the response content - LiteLLM standardizes the response structure based on OpenAI's format for completion calls.
        content = response.choices[0].message.content
        return content.strip() if content else ""

    except Exception as e:
        error_type = type(e).__name__
        print(f"\nERROR: LiteLLM API Error ({model}): {error_type} - {str(e)}", file=sys.stderr)
        # Re-raise the exception or return an error indicator string
        # raise # Option 1: Re-raise the exception
        return f"ERROR: LiteLLM call failed - {error_type}" # Option 2: Return error string

# =============================================================================
# SECTION 4: Main Execution Block
# =============================================================================
async def main():
    """Main asynchronous function for example usage."""

    system_instruction=""
    input_prompt=""
    response=""

    # --- Define Inputs ---
    system_instruction = '''Your goal is not to **answer** the input prompt, but to **rephrase** it. Your goal is to elevate low-value inputs into high-value outputs through inherent connectivity with its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message. '''
    system_instruction = '''Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.'''
    input_prompt="Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation."


    # =======================================================
    # [2025.05.17 13:01]
    system_instruction = '''Your goal is not to **answer** the input prompt, but to **rephrase** it. Your goal is to elevate low-value inputs into high-value outputs through inherent connectivity with its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message. '''
    input_prompt="The system is designed to transform LLM interactions from conversational exchanges into structured, function-like operations by applying a schema-driven approach to prompt engineering. It provides a framework for defining, organizing, and executing multi-step instruction sequences that process user inputs through a series of transformations, each following a precise pattern of role definition, input/output specification, and process steps. The core purpose is to enable more deterministic, reliable, and powerful LLM interactions by treating the LLM as a specialized component within a larger system rather than as a conversational partner."

    # --- Select Model and Parameters ---
    model_to_use = "gpt-4.1"
    temp = 0.15
    # max_tok = 500 # Optional

    print(f"\n--- Querying LLM ---")
    print(f"Model: {model_to_use}")
    print(f"Temperature: {temp}")
    # if max_tok: print(f"Max Tokens: {max_tok}")
    print(f"System Instruction (preview): {system_instruction}...")
    print(f"User Prompt (preview): {input_prompt}...")

    # --- Call the updated query_llm function ---
    response = await query_llm(
        system_instruction=system_instruction,
        input_prompt=input_prompt,
        model=model_to_use,
        temperature=temp,
        # max_tokens=max_tok # Pass max_tokens if needed
    )

    # --- Format and Print Interaction Log ---
    # Clean up strings for printing
    printable_system_instruction = system_instruction.replace('"', "'").replace("\n", ". ")
    printable_input_prompt = input_prompt.replace('"', "'").replace("\n", ". ")
    printable_response = str(response).replace('"', "'") # Keep newlines in response for readability

    print(f"\n--- Result ---")
    print(f"\nInput Prompt:\n```\n{input_prompt}\n```")
    print(f"\nSystem Instruction:\n```\n{system_instruction}\n```")
    print(f"\nResponse from {model_to_use}:\n```\n{response}\n```")

    # # Alternative compact print format:
    # print(f"\nhere's the result:\n```")
    # print(f'input_prompt='''{printable_input_prompt}'''\n')
    # print(f'system_instruction='''{printable_system_instruction}'''\n')
    # print(f'response='''{printable_response}'''\n')
    # print(f'```\n')


if __name__ == "__main__":
    # Run the main asynchronous function
    asyncio.run(main())
