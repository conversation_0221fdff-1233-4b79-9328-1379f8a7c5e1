
# Template Structure

```
# FILE ORGANIZATION
templates/
├── {system_id}-a-{system_name}-{component_function}.md
├── {system_id}-b-{system_name}-{component_function}.md
└── {system_id}-c-{system_name}-{component_function}.md

# EXAMPLES
templates/
├── 0001-a-rephrase-instruction-converter.md
├── 0001-b-rephrase-essence-distiller.md
└── etc

# HIERARCHICAL STRUCTURE
System ({system_id})
├── Template A ({component_function})
│   ├── [Title]
│   ├── Interpretation
│   └── `{Transformation}`
├── Template B ({component_function})
└── Template C ({component_function})

# TEMPLATE FORMAT
[Title] Interpretation Execute as: `{Transformation}`
  │      │              │         └─ Machine-parsable parameters
  │      │              └─ Standard connector phrase
  │      └─ Human-readable instructions
  └─ Template identifier

# COMPONENT VISUALIZATION

┌─ Title ─────────────────────────────────────┐
│ [Instruction Converter]                     │
└────────────────────────────────────────────┬┘
                                             │
┌─ Interpretation ───────────────────────┐   │
│ Your goal is not to **answer** the     │   │
│ input prompt, but to **rephrase** it,  │   │
│ and to do so by the parameters defined │   │
│ *inherently* within this message.      │   │
│ Execute as:                            │   │
└───────────────────────────────────────┬┘   │
                                        │    │
┌─ Transformation ───────────────────┐  │    │
│ `{                                 │  │    │
│   role=instruction_converter;      │  │    │
│   input=[original_text:str];       │◄─┴────┘
│   process=[
│     strip_first_person_references(),
│     convert_statements_to_directives(),
│     identify_key_actions(),
│     ...
│   ];
│   constraints=[
│     deliver_clear_actionable_commands(),
│     preserve_original_sequence(),
│     ...
│   ];
│   requirements=[
│     remove_self_references(),
│     use_command_voice(),
│     ...
│   ];
│   output={instruction_format:str}
│ }`
└─────────────────────────────────────┘

# TRANSFORMATION STRUCTURE

┌─ Role ──────────────────────────────────────┐
│ role={function_identifier}                  │
│ # Defines template's primary function       │
└────────────────────────────────────────────┬┘
                                             │
┌─ Input ─────────────────────────────────┐  │
│ input=[{parameter}:{type}]              │  │
│ # Specifies input parameters and types  │  │
└─────────────────────────────────────────┘  │
                                             │
┌─ Process ───────────────────────────────┐  │
│ process=[                               │  │
│   {operation_1}(),                      │  │
│   {operation_2}(),                      │◄─┘
│   ...
│ ]
│ # Defines processing operations
└─────────────────────────────────────────┘

┌─ Constraints ─────────────────────────────┐
│ constraints=[                             │
│   {constraint_1}(),                       │
│   {constraint_2}(),                       │
│   ...                                     │
│ ]                                         │
│ # Sets operational boundaries             │
└──────────────────────────────────────────┬┘
                                           │
┌─ Requirements ──────────────────────┐    │
│ requirements=[                      │    │
│   {requirement_1}(),                │    │
│   {requirement_2}(),                │    │
│   ...                               │    │
│ ]                                   │    │
│ # Defines mandatory behaviors       │    │
└────────────────────────────────────┬┘    │
                                     │     │
┌─ Output ─────────────────────┐     │     │
│ output={parameter:{type}}    │◄────┴─────┘
│ # Specifies return format    │
└─────────────────────────────┘

# METADATA
Template:
  keywords: "{keyword_1}|{keyword_2}|{keyword_3}"
  template_id: "{system_id}-{step}-{system_name}-{component_function}"

System:
  sequence_id: "{system_id}"
  steps: [
    "{system_id}-a-{system_name}-{component_function}",
    "{system_id}-b-{system_name}-{component_function}",
    ...
  ]
```

## Example Templates

```
[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.
`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`
```

```
[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.
`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`
```

```
[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:
`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`
```

```
[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`
```

```
[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`
```

---
